import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { formatCurrency } from "@/lib/utils";
import { CREDIT_AMOUNT } from "../constants";

// Define types for the component props
interface CreditConditionsStepProps {
  amount: number;
  setAmount: (amount: number) => void;
  nationalId: string;
  setNationalId: (id: string) => void;
  onNext: () => void;
}

/**
 * Combined step for credit amount and ID verification
 */
export function CreditConditionsStep({
  amount,
  setAmount,
  nationalId,
  setNationalId,
  onNext
}: CreditConditionsStepProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(amount.toString());

  const handleAmountClick = () => {
    setIsEditing(true);
    setInputValue(amount.toString());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputBlur = () => {
    const newAmount = parseInt(inputValue);
    if (!isNaN(newAmount) && newAmount >= CREDIT_AMOUNT.MIN && newAmount <= CREDIT_AMOUNT.MAX) {
      setAmount(newAmount);
    } else {
      setInputValue(amount.toString());
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-800">Condiciones Crédito</h2>
      
      {/* Credit Amount Section */}
      <div className="space-y-4">
        <p className="text-gray-600">Selecciona el monto que deseas solicitar</p>

        <div className="text-center mb-4">
          {isEditing ? (
            <div className="relative inline-block">
              <input
                type="number"
                value={inputValue}
                onChange={handleInputChange}
                onBlur={handleInputBlur}
                onKeyDown={handleKeyDown}
                min={CREDIT_AMOUNT.MIN}
                max={CREDIT_AMOUNT.MAX}
                autoFocus
                className="text-center text-xl font-bold w-40 p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          ) : (
            <span 
              className="text-3xl font-bold text-blue-600 cursor-pointer hover:underline"
              onClick={handleAmountClick}
            >
              {formatCurrency(amount)}
            </span>
          )}
          <p className="text-xs text-gray-500 mt-1">Toca el monto para editarlo directamente</p>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between text-sm text-gray-500">
            <span>{formatCurrency(CREDIT_AMOUNT.MIN)}</span>
            <span>{formatCurrency(CREDIT_AMOUNT.MAX)}</span>
          </div>

          <Slider
            value={[amount]}
            min={CREDIT_AMOUNT.MIN}
            max={CREDIT_AMOUNT.MAX}
            step={CREDIT_AMOUNT.STEP}
            onValueChange={(value) => setAmount(value[0])}
            className="py-4"
          />
        </div>
      </div>

      {/* ID Verification Section */}
      <div className="space-y-4 mt-8">
        <h3 className="text-lg font-medium text-gray-700">Verificación de Identidad</h3>
        <p className="text-gray-600">Ingresa tu número de identificación personal</p>
        
        <input
          type="text"
          placeholder="Número de identificación"
          value={nationalId}
          onChange={(e) => setNationalId(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div className="flex justify-center mt-8">
        <Button
          onClick={onNext}
          className="w-full max-w-[200px]"
          size="lg"
          disabled={!nationalId.trim()}
        >
          Siguiente
        </Button>
      </div>
    </div>
  );
}