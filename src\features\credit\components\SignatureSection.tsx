import React, { useState } from 'react';
import { useSignatureStore } from '../types/useSignatureStore';
import '@/styles/signature-pad.css';
import { Button } from '@/components/ui/button';
import { Trash2, Edit3 } from 'lucide-react';
import { SignatureModal } from '@/components/ui/signature-modal';

interface SignatureSectionProps {
  title?: string;
  description?: string;
  required?: boolean;
}

export const SignatureSection: React.FC<SignatureSectionProps> = ({
  title = 'Firma del solicitante',
  description = 'Por favor firma para confirmar que la información proporcionada es correcta.',
  required = true,
}) => {
  const { signatureDataUrl, setSignatureDataUrl } = useSignatureStore();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSaveSignature = (dataUrl: string) => {
    setSignatureDataUrl(dataUrl);
    setIsModalOpen(false);
  };

  const handleRemoveSignature = () => {
    setSignatureDataUrl(null);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
      <h3 className="text-lg font-medium text-gray-800 flex items-center">
        <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
          ✓
        </span>
        {title}
        {required && <span className="text-red-500 ml-1">*</span>}
      </h3>

      <p className="text-gray-600 text-sm ml-8 mb-4">
        {description}
      </p>

      {signatureDataUrl ? (
        <div className="mt-4 space-y-3">
          <div className="border rounded-md p-2 bg-gray-50">
            <img
              src={signatureDataUrl}
              alt="Firma del solicitante"
              className="signature-image max-h-[150px] mx-auto"
            />
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleOpenModal}
              className="flex-1 flex items-center justify-center"
            >
              <Edit3 className="h-4 w-4 mr-1" />
              Editar firma
            </Button>
            <Button
              variant="destructive"
              onClick={handleRemoveSignature}
              className="flex items-center justify-center"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Eliminar
            </Button>
          </div>
        </div>
      ) : (
        <div className="mt-4">
          <Button
            variant="outline"
            onClick={handleOpenModal}
            className="w-full py-8 border-dashed border-2 flex flex-col items-center justify-center"
          >
            <span className="text-2xl mb-2">✍️</span>
            <span className="font-medium">Haz clic para firmar</span>
            <span className="text-sm text-gray-500 mt-1">Se abrirá un área de firma a pantalla completa</span>
          </Button>
        </div>
      )}

      {/* Modal de firma a pantalla completa */}
      <SignatureModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveSignature}
        title={title}
        description={description}
      />
    </div>
  );
};

export default SignatureSection;
