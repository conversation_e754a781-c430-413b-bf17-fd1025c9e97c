import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { LoanTermStepProps } from "../types";
import { LOAN_TERM } from "../constants";

/**
 * Step for selecting the loan term
 */
export function LoanTermStep({
  term,
  setTerm,
  onNext,
  onPrevious
}: LoanTermStepProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-800">Pla<PERSON> del Crédito</h2>
      <p className="text-gray-600">Selecciona el plazo en meses</p>

      <div className="space-y-6 mt-6">
        <div className="text-center mb-4">
          <span className="text-3xl font-bold text-blue-600">{term} meses</span>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between text-sm text-gray-500">
            <span>{LOAN_TERM.MIN} meses</span>
            <span>{LOAN_TERM.MAX} meses</span>
          </div>

          <Slider
            value={[term]}
            min={LOAN_TERM.MIN}
            max={LOAN_TERM.MAX}
            step={LOAN_TERM.STEP}
            onValueChange={(value) => setTerm(value[0])}
            className="py-4"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-3 mt-8">
        <Button
          onClick={onPrevious}
          variant="outline"
          className="w-full"
          size="lg"
        >
          Anterior
        </Button>
        <Button
          onClick={onNext}
          className="w-full"
          size="lg"
        >
          Siguiente
        </Button>
      </div>
    </div>
  );
}
