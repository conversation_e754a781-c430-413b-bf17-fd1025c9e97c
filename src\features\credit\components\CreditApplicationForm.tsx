import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChevronRight, User, Star } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

// Constantes para los límites de monto y plazo
const CREDIT_AMOUNT = {
  MIN: 1000,
  MAX: 50000,
  DEFAULT: 20000,
  POPULAR: 20000 // El monto más pedido por los clientes
};

const LOAN_TERM = {
  MIN: 1,
  MAX: 32,
  DEFAULT: 16
};

interface CreditApplicationFormProps {
  onSubmit: (data: any) => void;
  onNext: () => void;
}

export function CreditApplicationForm({ onSubmit, onNext }: CreditApplicationFormProps) {
  // Estados para los campos del formulario
  const [amount, setAmount] = useState(CREDIT_AMOUNT.DEFAULT);
  const [term, setTerm] = useState(LOAN_TERM.DEFAULT);
  const [nationalId, setNationalId] = useState("3006 27645 0101");
  const [deliveryMethod, setDeliveryMethod] = useState("sucursal");
  const [currentStep, setCurrentStep] = useState(1);
  const [isValidating, setIsValidating] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [userInfo, setUserInfo] = useState({
    name: "",
    birthdate: "",
    location: "",
    civilStatus: "",
    gender: ""
  });

  // Función para validar el DPI
  const validateNationalId = () => {
    if (nationalId.length !== 13) {
      toast.error("El DPI debe tener 13 dígitos");
      return;
    }

    setIsValidating(true);

    // Simulamos una validación con API
    setTimeout(() => {
      setIsValidating(false);
      setIsValidated(true);
      setUserInfo({
        name: "Nombre Completo",
        birthdate: "Fecha de nacimiento",
        location: "Lugar",
        civilStatus: "Estado civil",
        gender: "Género"
      });
      toast.success("DPI validado correctamente");
    }, 1000);
  };

  // Función para formatear el plazo en meses
  const formatTerm = (value: number) => {
    return `${value} ${value === 1 ? 'mes' : 'meses'}`;
  };

  // Calcular la posición de la estrella en el slider
  const getStarPosition = () => {
    const percentage = ((CREDIT_AMOUNT.POPULAR - CREDIT_AMOUNT.MIN) / (CREDIT_AMOUNT.MAX - CREDIT_AMOUNT.MIN)) * 100;
    return `${percentage}%`;
  };

  return (
    <div className="max-w-md mx-auto bg-white">
      {/* Encabezado del formulario */}
      <div className="text-center mb-6">
        <h1 className="text-xl font-bold text-blue-700 mb-6">Formulario de solicitud</h1>

        {/* Indicador de pasos */}
        <div className="flex items-center justify-center mb-4">
          <div className="relative flex items-center w-full max-w-xs mx-auto">
            {/* Línea de progreso */}
            <div className="absolute h-0.5 bg-gray-200 left-0 right-0 top-1/2 transform -translate-y-1/2"></div>

            {/* Pasos */}
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="relative flex-1 flex justify-center">
                <div className={`w-7 h-7 rounded-full bg-gray-200 flex items-center justify-center z-10 ${
                  step === 1 ? 'bg-gray-200' : 'bg-gray-200'
                }`}>
                  <span className="text-gray-600 text-sm">{step}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Etiquetas de pasos */}
        <div className="flex text-xs text-gray-600 justify-between px-2 mb-8">
          <span className="flex-1 text-center font-medium">Condiciones</span>
          <span className="flex-1 text-center">Datos generales</span>
          <span className="flex-1 text-center">Fotografías</span>
          <span className="flex-1 text-center">Datos financieros</span>
        </div>
      </div>

      {/* Contenido del formulario */}
      <div className="bg-gray-50 rounded-lg p-6 shadow-sm space-y-4">
        {/* 1. Monto solicitado */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-7 h-7 rounded-full bg-gray-200 flex items-center justify-center mr-2">
              <span className="text-gray-600 text-sm">1</span>
            </div>
            <h3 className="text-blue-700 font-medium">Monto solicitado</h3>
          </div>

          <div className="mt-3">
            <div className="text-center mb-2">
              <span className="text-2xl font-bold">{formatCurrency(amount)}</span>
            </div>
            <p className="text-xs text-center text-blue-600 mb-3 flex items-center justify-center">
              <Star className="w-3 h-3 fill-blue-600 text-blue-600 mr-1" />
              El monto más pedido por nuestros clientes
            </p>

            <div className="relative my-4">
              <Slider
                value={[amount]}
                min={CREDIT_AMOUNT.MIN}
                max={CREDIT_AMOUNT.MAX}
                step={1000}
                onValueChange={(value) => setAmount(value[0])}
                className="my-4"
              />

              {/* Estrella indicadora del monto popular */}
              <div
                className="absolute top-4 transform -translate-y-1/2 z-10"
                style={{
                  left: getStarPosition(),
                  marginLeft: "-8px" // Ajuste para centrar la estrella
                }}
              >
                <Star className="w-4 h-4 fill-blue-600 text-blue-600" />
              </div>
            </div>

            <div className="flex justify-between text-xs text-gray-500">
              <span>GTQ 1,000</span>
              <span className="text-center">Selecciona el monto deseado</span>
              <span>GTQ 50,000</span>
            </div>
          </div>
        </div>

        {/* 2. Plazo */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-7 h-7 rounded-full bg-gray-200 flex items-center justify-center mr-2">
              <span className="text-gray-600 text-sm">2</span>
            </div>
            <h3 className="text-blue-700 font-medium">Plazo</h3>
          </div>

          <div className="mt-3">
            <div className="text-center mb-2">
              <span className="text-2xl font-bold">{formatTerm(term)}</span>
            </div>

            <Slider
              value={[term]}
              min={LOAN_TERM.MIN}
              max={LOAN_TERM.MAX}
              step={1}
              onValueChange={(value) => setTerm(value[0])}
              className="my-4"
            />

            <div className="flex justify-between text-xs text-gray-500">
              <span>1</span>
              <span className="text-center">Selecciona el plazo deseado</span>
              <span>32</span>
            </div>
          </div>
        </div>

        {/* 3. Verificación de identidad */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-7 h-7 rounded-full bg-gray-200 flex items-center justify-center mr-2">
              <span className="text-gray-600 text-sm">3</span>
            </div>
            <h3 className="text-blue-700 font-medium">Verificación de identidad</h3>
          </div>

          <div className="mt-3">
            <Label htmlFor="dpi" className="text-sm text-gray-600">Ingrese su DPI</Label>
            <div className="flex mt-1 gap-2">
              <Input
                id="dpi"
                value={nationalId}
                onChange={(e) => setNationalId(e.target.value.replace(/\D/g, ''))}
                placeholder="0 0 0 0  0 0 0 0 0  0 0 0 0"
                maxLength={13}
                className="flex-1"
              />
              <Button
                onClick={validateNationalId}
                disabled={nationalId.length !== 13 || isValidating || isValidated}
                className="bg-blue-700 hover:bg-blue-800"
              >
                {isValidating ? "..." : "Validar"}
              </Button>
            </div>
            <p className="text-xs text-gray-500 mt-1">* Ingreselo sin guiones</p>

            {isValidated && (
              <div className="mt-4 flex items-start">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                  <User className="w-5 h-5 text-gray-500" />
                </div>
                <div className="text-xs text-gray-500">
                  <p className="text-gray-700">Nombre Completo</p>
                  <p>Fecha de nacimiento - Lugar</p>
                  <p>Estado civil - Género</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 4. Forma de desembolso */}
        <div className="bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center mb-3">
            <div className="w-7 h-7 rounded-full bg-gray-200 flex items-center justify-center mr-2">
              <span className="text-gray-600 text-sm">4</span>
            </div>
            <h3 className="text-blue-700 font-medium">Forma de desembolso</h3>
          </div>

          <div className="mt-3">
            <Select onValueChange={setDeliveryMethod} value={deliveryMethod}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Sucursal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sucursal">Sucursal</SelectItem>
                <SelectItem value="cajero">Cajero</SelectItem>
                <SelectItem value="transferencia">Transferencia</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Botón de siguiente */}
      <div className="flex justify-end mt-4">
        <Button
          onClick={onNext}
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-full w-10 h-10 p-0 flex items-center justify-center"
        >
          <ChevronRight className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
}
