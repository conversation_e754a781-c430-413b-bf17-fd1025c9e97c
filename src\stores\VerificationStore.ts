// import { create } from 'zustand';
// import { authService } from '@/services';

// interface VerificationState {
//   // User identification
//   nationalId: string;
  
//   // Process state
//   processId: string | null;
//   stage: 'welcome' | 'front' | 'back' | 'selfie' | 'complete';
//   isLoading: boolean;
//   error: string | null;
  
//   // Photo files
//   frontIdPhoto: File | null;
//   backIdPhoto: File | null;
//   selfiePhoto: File | null;
  
//   // Privacy settings
//   privacyAccepted: boolean;
  
//   // Actions
//   setNationalId: (id: string) => void;
//   setPrivacyAccepted: (accepted: boolean) => void;
//   initVerificationProcess: () => Promise<boolean>;
//   setStage: (stage: 'welcome' | 'front' | 'back' | 'selfie' | 'complete') => void;
//   setFrontIdPhoto: (photo: File | null) => void;
//   setBackIdPhoto: (photo: File | null) => void;
//   setSelfiePhoto: (photo: File | null) => void;
//   validateFrontDpi: (photo: File) => Promise<boolean>;
//   uploadFrontId: () => Promise<boolean>;
//   uploadBackId: () => Promise<boolean>;
//   uploadSelfie: () => Promise<boolean>;
//   completeVerification: () => Promise<boolean>;
//   reset: () => void;
//   validateBackDpi: (file: File) => Promise<boolean>;
//   validateSelfie: (file: File) => Promise<boolean>;
// }

// export const useVerificationStore = create<VerificationState>((set, get) => ({
//   // Initial state
//   nationalId: '',
//   processId: null,
//   stage: 'welcome',
//   isLoading: false,
//   error: null,
//   frontIdPhoto: null,
//   backIdPhoto: null,
//   selfiePhoto: null,
//   privacyAccepted: false,
  
//   // Actions
//   setNationalId: (id) => set({ nationalId: id }),
  
//   setPrivacyAccepted: (accepted) => set({ privacyAccepted: accepted }),
  
//   initVerificationProcess: async () => {
//     const { nationalId } = get();
    
//     // Validate that we have a national ID
//     if (!nationalId.trim()) {
//       set({ error: "Por favor, ingrese un número de identificación válido" });
//       return false;
//     }
    
//     set({ isLoading: true, error: null });
    
//     try {
//       // Use the nationalId from the store instead of hardcoded value
//       const response = await authService.initProcessWithDpiNumber(nationalId);
      
//       if (response.success && response.details) {
//         set({ 
//           processId: response.details,
//           isLoading: false
//         });
//         return true;
//       } else {
//         set({ 
//           error: response.error || "Error al iniciar el proceso de verificación", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err: any) {
//       console.error("Error al iniciar el proceso:", err);
//       set({ 
//         error: "Ha ocurrido un error inesperado. Por favor, intenta nuevamente.", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   setStage: (stage) => set({ stage }),
  
//   setFrontIdPhoto: (photo) => set({ frontIdPhoto: photo }),
  
//   setBackIdPhoto: (photo) => set({ backIdPhoto: photo }),
  
//   setSelfiePhoto: (photo) => set({ selfiePhoto: photo }),
  
  
//   validateFrontDpi: async (photo) => {
//     const { processId } = get();
//     if (!processId) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.validateFrontDpiWithAws(processId, photo);
      
//       // The service returns the response with a success property AND
//       // the API's response is spread inside, which includes the error property
//       if (response.success) {
//         // Now check the API's actual response for error: false
//         if (response.error === false) {
//           // Validation successful - API returned error: false
//           set({ isLoading: false });
//           return true;
//         } else {
//           // API returned error: true with a message
//           set({ 
//             error: response.mensage || "Error al validar el documento", 
//             isLoading: false 
//           });
//           return false;
//         }
//       } else {
//         // The service call itself failed
//         set({ 
//           error: response.error || "Error al validar el documento", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err) {
//       console.error("Error validating front DPI:", err);
//       set({ 
//         error: "Error al comunicarse con el servicio de validación", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },

//   validateBackDpi: async (photo) => {
//     const { processId } = get();
//     if (!processId) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.validateBackDpiWithAws(processId, photo);
      
//       // Check if the service call was successful
//       if (response.success) {
//         // Now check if the API validation was successful (error: false)
//         if (response.error === false) {
//           // Validation successful
//           set({ isLoading: false });
//           return true;
//         } else {
//           // API returned an error
//           set({ 
//             error: response.mensage || "Error al validar el reverso del documento", 
//             isLoading: false 
//           });
//           return false;
//         }
//       } else {
//         // The service call itself failed
//         set({ 
//           error: response.error || "Error al validar el reverso del documento", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err) {
//       console.error("Error validating back DPI:", err);
//       set({ 
//         error: "Error al comunicarse con el servicio de validación", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   validateSelfie: async (photo) => {
//     const { processId } = get();
//     if (!processId) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.validateSelfieVideo(processId, photo);
      
//       // Check if the service call was successful
//       if (response.success) {
//         // Now check if the API validation was successful (error: false)
//         if (response.error === false) {
//           // Validation successful
//           set({ isLoading: false });
//           return true;
//         } else {
//           // API returned an error
//           set({ 
//             error: response.mensage || "Error al validar el video selfie", 
//             isLoading: false 
//           });
//           return false;
//         }
//       } else {
//         // The service call itself failed
//         set({ 
//           error: response.error || "Error al validar el video selfie", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err) {
//       console.error("Error validating selfie video:", err);
//       set({ 
//         error: "Error al comunicarse con el servicio de validación", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   uploadFrontId: async () => {
//     const { processId, frontIdPhoto } = get();
//     if (!processId || !frontIdPhoto) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.uploadProcessImage(processId, 'frontId', frontIdPhoto);
      
//       if (response.success) {
//         set({ isLoading: false });
//         return true;
//       } else {
//         set({ 
//           error: response.error || "Error al subir la imagen frontal del DPI", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err: any) {
//       console.error("Error al subir imagen frontal:", err);
//       set({ 
//         error: "Error al subir la imagen frontal", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   uploadBackId: async () => {
//     const { processId, backIdPhoto } = get();
//     if (!processId || !backIdPhoto) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.uploadProcessImage(processId, 'backId', backIdPhoto);
      
//       if (response.success) {
//         set({ isLoading: false });
//         return true;
//       } else {
//         set({ 
//           error: response.error || "Error al subir la imagen trasera del DPI", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err: any) {
//       console.error("Error al subir imagen trasera:", err);
//       set({ 
//         error: "Error al subir la imagen trasera", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   uploadSelfie: async () => {
//     const { processId, selfiePhoto } = get();
//     if (!processId || !selfiePhoto) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.uploadProcessImage(processId, 'selfie', selfiePhoto);
      
//       if (response.success) {
//         set({ isLoading: false });
//         return true;
//       } else {
//         set({ 
//           error: response.error || "Error al subir la selfie", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err: any) {
//       console.error("Error al subir selfie:", err);
//       set({ 
//         error: "Error al subir la selfie", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   completeVerification: async () => {
//     const { processId } = get();
//     if (!processId) return false;
    
//     set({ isLoading: true, error: null });
    
//     try {
//       const response = await authService.completeVerificationProcess(processId);
      
//       if (response.success) {
//         set({ 
//           isLoading: false, 
//           stage: 'complete' 
//         });
//         return true;
//       } else {
//         set({ 
//           error: response.error || "Error al completar el proceso de verificación", 
//           isLoading: false 
//         });
//         return false;
//       }
//     } catch (err: any) {
//       console.error("Error al completar verificación:", err);
//       set({ 
//         error: "Ha ocurrido un error al finalizar el proceso", 
//         isLoading: false 
//       });
//       return false;
//     }
//   },
  
//   reset: () => set({
//     nationalId: '',
//     processId: null,
//     stage: 'welcome',
//     isLoading: false,
//     error: null,
//     frontIdPhoto: null,
//     backIdPhoto: null,
//     selfiePhoto: null,
//     privacyAccepted: false
//   })
// }));

import { create } from 'zustand';
import { authService } from '@/services';

// Set this to true for development (all validations will pass)
const DEV_MODE = true;

interface VerificationState {
  // User identification
  nationalId: string;
  
  // Process state
  processId: string | null;
  stage: 'welcome' | 'front' | 'back' | 'selfie' | 'complete';
  isLoading: boolean;
  error: string | null;
  
  // Photo files
  frontIdPhoto: File | null;
  backIdPhoto: File | null;
  selfiePhoto: File | null;
  
  // Privacy settings
  privacyAccepted: boolean;
  
  // Actions
  setNationalId: (id: string) => void;
  setPrivacyAccepted: (accepted: boolean) => void;
  initVerificationProcess: () => Promise<boolean>;
  setStage: (stage: 'welcome' | 'front' | 'back' | 'selfie' | 'complete') => void;
  setFrontIdPhoto: (photo: File | null) => void;
  setBackIdPhoto: (photo: File | null) => void;
  setSelfiePhoto: (photo: File | null) => void;
  validateFrontDpi: (photo: File) => Promise<boolean>;
  uploadFrontId: () => Promise<boolean>;
  uploadBackId: () => Promise<boolean>;
  uploadSelfie: () => Promise<boolean>;
  completeVerification: () => Promise<boolean>;
  reset: () => void;
  validateBackDpi: (file: File) => Promise<boolean>;
  validateSelfie: (file: File) => Promise<boolean>;
}

// Helper function for development mode delays
const mockDelay = () => new Promise(resolve => setTimeout(resolve, 800));

export const useVerificationStore = create<VerificationState>((set, get) => ({
  // Initial state
  nationalId: '',
  processId: null,
  stage: 'welcome',
  isLoading: false,
  error: null,
  frontIdPhoto: null,
  backIdPhoto: null,
  selfiePhoto: null,
  privacyAccepted: false,
  
  // Actions
  setNationalId: (id) => set({ nationalId: id }),
  
  setPrivacyAccepted: (accepted) => set({ privacyAccepted: accepted }),
  
  initVerificationProcess: async () => {
    const { nationalId } = get();
    
    if (!nationalId.trim()) {
      set({ error: "Por favor, ingrese un número de identificación válido" });
      return false;
    }
    
    set({ isLoading: true, error: null });
    
    if (DEV_MODE) {
      await mockDelay();
      set({ processId: 'dev-process-123', isLoading: false });
      console.log("DEV MODE: Process initialized with mock ID");
      return true;
    }
    
    try {
      const response = await authService.initProcessWithDpiNumber(nationalId);
      
      if (response.success && response.details) {
        set({ processId: response.details, isLoading: false });
        return true;
      } else {
        set({ error: response.error || "Error al iniciar el proceso", isLoading: false });
        return false;
      }
    } catch (err: any) {
      console.error("Error al iniciar el proceso:", err);
      set({ error: "Ha ocurrido un error inesperado", isLoading: false });
      return false;
    }
  },
  
  setStage: (stage) => set({ stage }),
  
  setFrontIdPhoto: (photo) => set({ frontIdPhoto: photo }),
  
  setBackIdPhoto: (photo) => set({ backIdPhoto: photo }),
  
  setSelfiePhoto: (photo) => set({ selfiePhoto: photo }),
  
  validateFrontDpi: async (photo) => {
    if (DEV_MODE) {
      await mockDelay();
      console.log("DEV MODE: Front DPI validation passed");
      return true;
    }
    
    const { processId } = get();
    if (!processId) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.validateFrontDpiWithAws(processId, photo);
      
      if (response.success && response.error === false) {
        set({ isLoading: false });
        return true;
      } else {
        set({ 
          error: response.mensage || "Error al validar el documento", 
          isLoading: false 
        });
        return false;
      }
    } catch (err) {
      console.error("Error validating front DPI:", err);
      set({ error: "Error al comunicarse con el servicio", isLoading: false });
      return false;
    }
  },

  validateBackDpi: async (photo) => {
    if (DEV_MODE) {
      await mockDelay();
      console.log("DEV MODE: Back DPI validation passed");
      return true;
    }
    
    const { processId } = get();
    if (!processId) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.validateBackDpiWithAws(processId, photo);
      
      if (response.success && response.error === false) {
        set({ isLoading: false });
        return true;
      } else {
        set({ 
          error: response.mensage || "Error al validar el reverso", 
          isLoading: false 
        });
        return false;
      }
    } catch (err) {
      console.error("Error validating back DPI:", err);
      set({ error: "Error al comunicarse con el servicio", isLoading: false });
      return false;
    }
  },
  
  validateSelfie: async (photo) => {
    if (DEV_MODE) {
      await mockDelay();
      console.log("DEV MODE: Selfie validation passed");
      return true;
    }
    
    const { processId } = get();
    if (!processId) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.validateSelfieVideo(processId, photo);
      
      if (response.success && response.error === false) {
        set({ isLoading: false });
        return true;
      } else {
        set({ 
          error: response.mensage || "Error al validar el video selfie", 
          isLoading: false 
        });
        return false;
      }
    } catch (err) {
      console.error("Error validating selfie video:", err);
      set({ error: "Error al comunicarse con el servicio", isLoading: false });
      return false;
    }
  },
  
  uploadFrontId: async () => {
    if (DEV_MODE) {
      await mockDelay();
      console.log("DEV MODE: Front ID uploaded successfully");
      return true;
    }
    
    const { processId, frontIdPhoto } = get();
    if (!processId || !frontIdPhoto) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.uploadProcessImage(processId, 'frontId', frontIdPhoto);
      
      if (response.success) {
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || "Error al subir la imagen frontal", isLoading: false });
        return false;
      }
    } catch (err: any) {
      console.error("Error al subir imagen frontal:", err);
      set({ error: "Error al subir la imagen frontal", isLoading: false });
      return false;
    }
  },
  
  uploadBackId: async () => {
    if (DEV_MODE) {
      await mockDelay();
      console.log("DEV MODE: Back ID uploaded successfully");
      return true;
    }
    
    const { processId, backIdPhoto } = get();
    if (!processId || !backIdPhoto) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.uploadProcessImage(processId, 'backId', backIdPhoto);
      
      if (response.success) {
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || "Error al subir la imagen trasera", isLoading: false });
        return false;
      }
    } catch (err: any) {
      console.error("Error al subir imagen trasera:", err);
      set({ error: "Error al subir la imagen trasera", isLoading: false });
      return false;
    }
  },
  
  uploadSelfie: async () => {
    if (DEV_MODE) {
      await mockDelay();
      console.log("DEV MODE: Selfie uploaded successfully");
      return true;
    }
    
    const { processId, selfiePhoto } = get();
    if (!processId || !selfiePhoto) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.uploadProcessImage(processId, 'selfie', selfiePhoto);
      
      if (response.success) {
        set({ isLoading: false });
        return true;
      } else {
        set({ error: response.error || "Error al subir la selfie", isLoading: false });
        return false;
      }
    } catch (err: any) {
      console.error("Error al subir selfie:", err);
      set({ error: "Error al subir la selfie", isLoading: false });
      return false;
    }
  },
  
  completeVerification: async () => {
    if (DEV_MODE) {
      await mockDelay();
      set({ stage: 'complete' });
      console.log("DEV MODE: Verification completed successfully");
      return true;
    }
    
    const { processId } = get();
    if (!processId) return false;
    
    set({ isLoading: true, error: null });
    
    try {
      const response = await authService.completeVerificationProcess(processId);
      
      if (response.success) {
        set({ isLoading: false, stage: 'complete' });
        return true;
      } else {
        set({ error: response.error || "Error al completar el proceso", isLoading: false });
        return false;
      }
    } catch (err: any) {
      console.error("Error al completar verificación:", err);
      set({ error: "Ha ocurrido un error al finalizar el proceso", isLoading: false });
      return false;
    }
  },
  
  reset: () => set({
    nationalId: '',
    processId: null,
    stage: 'welcome',
    isLoading: false,
    error: null,
    frontIdPhoto: null,
    backIdPhoto: null,
    selfiePhoto: null,
    privacyAccepted: false
  })
}));