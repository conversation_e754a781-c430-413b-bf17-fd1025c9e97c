import React, { ReactNode } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  className?: string;
  fullScreen?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className,
  fullScreen = false,
}) => {
  if (!isOpen) return null;

  // Prevenir que el clic en el contenido del modal cierre el modal
  const handleContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
      onClick={onClose}
      style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <div
        className={cn(
          'bg-white rounded-lg shadow-xl overflow-hidden',
          fullScreen ? 'fixed inset-0 m-0' : 'max-w-lg w-full mx-4',
          className
        )}
        onClick={handleContentClick}
        style={{ position: 'relative', zIndex: 10000 }}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">{title}</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        <div className={fullScreen ? 'h-[calc(100vh-4rem)]' : ''}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
