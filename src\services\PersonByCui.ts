
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { useClientFormStore } from '@/features/credit/types/useClientFormStore';

/**
 * Generic API response interface
 */
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    details?: string;
}


class LoanRequestService {
    private api: AxiosInstance;
    private readonly baseUrl = 'api/v1/renap';

    constructor() {
        // Initialize Axios instance with base URL from environment variables
        this.api = axios.create({
            baseURL: import.meta.env.VITE_API_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout: 30000 // 30 seconds timeout
        });

        // Set up request and response interceptors
        this.setupInterceptors();
    }

    /**
     * Configure Axios interceptors for request and response handling
     */
    private setupInterceptors(): void {
        // Request interceptor - runs before each request
        this.api.interceptors.request.use(
            (config) => {
                // Get auth token if it exists
                const token = localStorage.getItem('auth_token');

                // Add token to Authorization header if available
                if (token && config.headers) {
                    config.headers.Authorization = `Bearer ${token}`;
                }

                console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, config.data || '');
                return config;
            },
            (error) => {
                console.error('Request error:', error);
                return Promise.reject(this.formatError(error));
            }
        );

        // Response interceptor - runs after each response
        this.api.interceptors.response.use(
            (response) => {
                console.log(`API Response [${response.status}]:`, response.data);
                return response;
            },
            (error) => {
                console.error('Response error:', error);
                return Promise.reject(this.formatError(error));
            }
        );
    }

    /**
     * Format error responses consistently
     */
    private formatError(error: AxiosError): any {
        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            const status = error.response.status;

            // Handle 401 Unauthorized
            if (status === 401) {
                localStorage.removeItem('auth_token');
                // You can redirect to login page here if needed
                // window.location.href = '/login';
            }

            return {
                success: false,
                status,
                error: error.message,
                details: error.response.data
            };
        } else if (error.request) {
            // The request was made but no response was received
            return {
                success: false,
                error: 'No response received from server',
                details: 'Please check your network connection'
            };
        } else {
            // Something happened in setting up the request
            return {
                success: false,
                error: error.message || 'Unknown error occurred',
                details: 'Error setting up the request'
            };
        }
    }

    /**
     * Generic GET request method
     */
    private async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        try {
            const response = await this.api.get<T>(url, config);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error(`GET ${url} error:`, error);
            return error;
        }
    }

    /**
     * Generic POST request method
     */
    private async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        try {
            const response = await this.api.post<T>(url, data, config);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error(`POST ${url} error:`, error);
            return error;
        }
    }

    /**
     * Generic file upload method using FormData
     */
    private async upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
        try {
            const uploadConfig: AxiosRequestConfig = {
                ...config,
                headers: {
                    ...config?.headers,
                    'Content-Type': 'multipart/form-data'
                }
            };

            const response = await this.api.post<T>(url, formData, uploadConfig);
            return {
                success: true,
                data: response.data
            };
        } catch (error: any) {
            console.error(`UPLOAD ${url} error:`, error);
            return error;
        }
    }

    async getPersonByCui(cui: string): Promise<ApiResponse<any>> {
        try {
            const url = `${this.baseUrl}/person/${cui}`;

            const headers = {
                'Accept': '*/*',
                // Si necesitas un token de autorización, agrégalo aquí:
                // 'Authorization': `Bearer TU_TOKEN`
            };

            const response = await this.get<any>(url, { headers });

            if (response.success && response.data && response.data.data?.length > 0) {
                const person = response.data.data[0];
                const setField = useClientFormStore.getState().setField;

                // Mapear campos
                setField('name1', person.PRIMER_NOMBRE || '');
                setField('name2', person.SEGUNDO_NOMBRE || '');
                setField('name3', ''); // No hay tercer nombre en la respuesta, ajustar si aplica
                setField('lastName1', person.PRIMER_APELLIDO || '');
                setField('lastName2', person.SEGUNDO_APELLIDO || '');
                setField('birth', person.FECHA_NACIMIENTO || '');
                setField('sexId', person.GENERO === 'M' ? 1 : person.GENERO === 'F' ? 2 : 0);
                setField('marital', person.ESTADO_CIVIL === 'C' ? 2 : person.ESTADO_CIVIL === 'S' ? 1 : 0);
                // Puedes mapear más campos como educación, ingresos, teléfono si vienen
            }
            
            return response;
        } catch (error: any) {
            console.error('Error fetching person data:', error);
            return {
                success: false,
                error: error.message || 'Failed to fetch person data',
                details: error.details || 'Unknown error occurred'
            };
        }
    }



    getInstance(): AxiosInstance {
        return this.api;
    }


}


// Create and export a singleton instance
export const personRequestService = new LoanRequestService();
export default personRequestService;


