import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { ChevronRight, Volume2 } from "lucide-react";

export default function WelcomePage() {
  const navigate = useNavigate();
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-blue-600 text-white">
      {/* Contenido principal */}
      <div className="w-full max-w-md px-6 py-10 flex flex-col items-center">
        {/* Encabezado */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-1">
            ¡Es tu <span className="text-white">oportunidad</span>!
          </h1>
          
          {/* Ilustración de persona con megáfono */}
          <div className="relative h-64 w-full my-6">
            {/* Megáfono con ondas */}
            <div className="absolute right-1/4 top-10">
              <Volume2 className="h-16 w-16 transform -rotate-45" />
              <div className="absolute -right-4 -top-4">
                <div className="w-2 h-8 bg-white transform rotate-45 rounded-full absolute -right-6 -top-2"></div>
                <div className="w-2 h-8 bg-white transform rotate-[25deg] rounded-full absolute -right-2 -top-4"></div>
                <div className="w-2 h-8 bg-white transform rotate-[65deg] rounded-full absolute -right-10 -top-4"></div>
              </div>
            </div>
            
            {/* Reloj/Cronómetro */}
            <div className="absolute right-10 top-6">
              <div className="w-12 h-12 rounded-full border-4 border-white relative">
                <div className="absolute top-1/2 left-1/2 w-5 h-1 bg-white transform -translate-x-1/2 -translate-y-1/2 rotate-45 origin-left"></div>
                <div className="absolute top-1/2 left-1/2 w-3 h-1 bg-white transform -translate-x-1/2 -translate-y-1/2 -rotate-45 origin-left"></div>
              </div>
              <div className="absolute -right-2 -top-2 w-8 h-8 border-t-4 border-r-4 border-white rounded-tr-full"></div>
              <div className="absolute -right-4 -top-4 w-12 h-12 border-t-4 border-r-4 border-white rounded-tr-full"></div>
            </div>
            
            {/* Silueta de persona */}
            <div className="absolute bottom-0 right-0 w-32 h-64 overflow-hidden">
              <div className="absolute bottom-0 right-0 w-32 h-64 bg-blue-700 rounded-tl-[100px]"></div>
              <div className="absolute bottom-0 right-8 w-16 h-56 bg-blue-600 rounded-tl-[80px]"></div>
            </div>
          </div>
        </div>
        
        {/* Características en píldoras */}
        <div className="w-full space-y-3 mb-12">
          <div className="bg-white text-blue-600 font-semibold py-2 px-4 rounded-full text-center">
            Hasta Q. 50,000
          </div>
          <div className="bg-white text-blue-600 font-semibold py-2 px-4 rounded-full text-center">
            Sin papeleo
          </div>
          <div className="bg-white text-blue-600 font-semibold py-2 px-4 rounded-full text-center">
            Aprobación en 5 minutos
          </div>
          <div className="bg-white text-blue-600 font-semibold py-2 px-4 rounded-full text-center">
            Seguro y confiable
          </div>
        </div>
        
        {/* Llamado a la acción */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-1">
            ¡Tu Crédito Digital <span className="text-white">en minutos</span>!
          </h2>
          <p className="text-blue-100">
            Completa tus datos, empieza ahora.
          </p>
        </div>
        
        {/* Botón de acción */}
        <Button 
          onClick={() => navigate('/terms')}
          className="bg-blue-800 hover:bg-blue-900 text-white rounded-full w-12 h-12 flex items-center justify-center self-end"
          size="icon"
        >
          <ChevronRight className="h-6 w-6" />
        </Button>
      </div>
    </div>
  );
}
