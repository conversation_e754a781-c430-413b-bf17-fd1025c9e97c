import { StepIndicatorProps } from "../types";
import { STEP_LABELS } from "../constants";

/**
 * Step indicator showing progress through the application
 */
export function StepIndicator({ currentStep, totalSteps }: StepIndicatorProps) {
  return (
    <div className="mb-8">
      <div className="flex justify-between relative">
        {/* Progress connecting lines - render behind circles */}
        <div className="absolute top-4 left-8 right-8 h-0.5 bg-gray-200 z-0"></div>
        <div
          className="absolute top-4 left-8 h-0.5 bg-blue-600 z-0 transition-all duration-300"
          style={{
            width: `calc(${(currentStep / (totalSteps - 1)) * 100}% - 32px)`
          }}
        ></div>

        {/* Step circles */}
        {STEP_LABELS.slice(0, totalSteps).map((label, index) => (
          <div
            key={label}
            className="flex flex-col items-center relative z-10"
          >
            <div
              className={`w-8 h-8 flex items-center justify-center rounded-full ${
                index < currentStep
                  ? "bg-blue-600 text-white"
                  : index === currentStep
                  ? "bg-blue-600 text-gray-600 p-0.5"
                  : "bg-gray-200 text-gray-600"
              }`}
            >
              {index === currentStep ? (
                <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
                  {index + 1}
                </div>
              ) : (
                index + 1
              )}
            </div>
            <span className={`text-xs mt-1 font-medium text-center max-w-16 ${
              index <= currentStep ? "text-blue-600" : "text-gray-500"
            }`}>
              {label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}