import { useEffect, useState, useRef } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { useReferencesFormStore } from "../types/useReferencesFormStore";
import { validateReferences } from "@/stores/validateReferences";
import { PartyPopper, UserPlus } from "lucide-react"; // Añadimos UserPlus
import "@/styles/confetti.css";

// Importamos el componente de firma
import SignatureSection from "./SignatureSection";
import { useSignatureStore } from "../types/useSignatureStore";


// Importamos todos los stores que necesitamos limpiar
import { useWorkFormStore } from "../types/useWorkFormStore";
import { useResidenceFormStore } from "../types/useResidenceFormStore";
import { useIncomeStore } from "../types/useIncomeStore";
import { useClientFormStore } from "../types/useClientFormStore";

interface DataReferenciasStepProps {
    onSubmit: () => Promise<void>;
    onNext: () => void;
    onPrevious?: () => void;
}

export function DataReferenciasStep({ onSubmit, onPrevious }: DataReferenciasStepProps) {
    // Referencias store
    const { references, setReferenceField, signatureImage, setSignatureImage, reset: resetReferences } = useReferencesFormStore();
    const { isValid: referencesValid } = validateReferences(references);

    // Otros stores que necesitamos limpiar
    const { reset: resetWork } = useWorkFormStore();
    const { reset: resetResidence } = useResidenceFormStore();
    const { reset: resetClient } = useClientFormStore();

    // Verificar si el formulario es válido (referencias y firma)
    const isValid = referencesValid && signatureImage !== null;

    // // Cargar las sucursales desde el JSON
    // useEffect(() => {
    //     setBranches(branchesData.branches as Branch[]);
    // }, [setBranches]);


    const [errors, setErrors] = useState<Array<Record<string, string | null>>>([{}, {}, {}]); // Ahora son 3 arrays
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);
    const [showSuccessAlert, setShowSuccessAlert] = useState(false);
    const [redirectCountdown, setRedirectCountdown] = useState(10);

    // Estado para rastrear campos tocados
    const [touchedFields, setTouchedFields] = useState<Array<Record<string, boolean>>>([{}, {}, {}]); // Ahora son 3 arrays

    const originalValues = useRef(references);

    useEffect(() => {
        const changed = references.some((ref, i) => {
            const orig = originalValues.current[i];
            return (
                ref.fullName !== orig.fullName ||
                ref.phone !== orig.phone ||
                ref.address !== orig.address ||
                ref.relationship !== orig.relationship
            );
        });

        setHasChanges(changed);
    }, [references]);

    // Efecto para manejar la cuenta regresiva y redirección
    useEffect(() => {
        if (!showSuccessAlert) return;

        if (redirectCountdown <= 0) {
            // Redirigir al inicio del formulario
            window.location.href = "/"; // Ajusta esta URL según la estructura de tu aplicación
            return;
        }

        const timer = setTimeout(() => {
            setRedirectCountdown(prev => prev - 1);
        }, 1000);

        return () => clearTimeout(timer);
    }, [showSuccessAlert, redirectCountdown]);

    const handleFieldChange = (index: 0 | 1 | 2, field: keyof typeof references[0], value: string) => {
        setReferenceField(index, field, value);

        // Marcar el campo como tocado
        setTouchedFields(prev => {
            const newTouchedFields = [...prev];
            newTouchedFields[index] = {
                ...newTouchedFields[index],
                [field]: true
            };
            return newTouchedFields;
        });

        // Validar el campo inmediatamente
        validateField(index, field, value);
    };

    // Función para validar un campo específico
    const validateField = (index: 0 | 1 | 2, field: keyof typeof references[0], value: string) => {
        // Crear una copia de las referencias para validar solo este campo
        const referencesToValidate = [...references];
        referencesToValidate[index] = {
            ...referencesToValidate[index],
            [field]: value
        };

        // Validar usando la función existente
        const { errors: newErrors } = validateReferences(referencesToValidate);

        // Actualizar solo el error del campo específico
        setErrors(prev => {
            const updatedErrors = [...prev];
            updatedErrors[index] = {
                ...updatedErrors[index],
                [field]: newErrors[index][field]
            };
            return updatedErrors;
        });
    };

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handlePrevious = () => {
        if (onPrevious) {
            scrollToTop();
            onPrevious();
        }
    };

    const handleNext = async () => {
        const { isValid: formIsValid, errors } = validateReferences(references);
        setErrors(errors);

        // Marcar todos los campos como tocados cuando se intenta enviar el formulario
        setTouchedFields([
            {
                fullName: true,
                phone: true,
                address: true,
                relationship: true
            },
            {
                fullName: true,
                phone: true,
                address: true,
                relationship: true
            },
            {
                fullName: true,
                phone: true,
                address: true,
                relationship: true
            }
        ]);

        if (!formIsValid) {
            toast.error("Por favor completa los campos de referencias requeridos.");
            return;
        }

        if (!signatureImage) {
            toast.error("Por favor firma el formulario para continuar.");
            return;
        }

        if (!hasChanges) {
            showSuccessMessage();
            return;
        }

        setIsSubmitting(true);
        try {
            await onSubmit();
            originalValues.current = [...references];
            showSuccessMessage();
        } catch (err: any) {
            toast.error("Error al enviar", { description: err?.message || "Error desconocido" });
        } finally {
            setIsSubmitting(false);
        }
    };


    // Función para mostrar el mensaje de éxito y comenzar la cuenta regresiva
    const showSuccessMessage = () => {
        // Determinar si es dispositivo móvil
        const isMobile = window.innerWidth < 768;

        // Mostrar toast de éxito con animación de confeti
        toast.success("¡Solicitud enviada con éxito!", {
            description: isMobile ? "¡Redirigiendo!" : "Serás redirigido al inicio en 10 segundos.",
            duration: 10000, // 10 segundos
            position: "top-center",
            className: "bg-green-50 border-2 border-green-200 text-green-700 shadow-lg",
            icon: <PartyPopper className="h-5 w-5 text-green-500" />,
            style: {
                fontSize: isMobile ? '14px' : '16px',
                padding: isMobile ? '8px' : '12px',
                maxWidth: isMobile ? '90vw' : '400px',
            },
        });

        // Crear efecto de confeti con CSS
        const confettiContainer = document.createElement('div');
        confettiContainer.className = 'confetti-container';
        document.body.appendChild(confettiContainer);

        // Determinar la cantidad de confeti según el tamaño de la pantalla
        const confettiCount = isMobile ? 30 : 50;

        // Crear piezas de confeti
        for (let i = 0; i < confettiCount; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';

            // Distribuir el confeti por toda la pantalla
            confetti.style.left = Math.random() * 100 + 'vw';

            // Retrasar la animación para que no caigan todos a la vez
            confetti.style.animationDelay = Math.random() * 3 + 's';

            // Colores festivos
            const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];

            // Añadir el confeti al contenedor
            confettiContainer.appendChild(confetti);
        }

        // Eliminar el confeti después de la animación
        setTimeout(() => {
            confettiContainer.remove();
        }, 10000);

        // Limpiar todos los stores
        resetReferences();
        resetWork();
        resetResidence();
        // Para useIncomeStore que no tiene reset
        useIncomeStore.setState({ incomeSourceTypeId: null });
        resetClient();
        setSignatureImage(null)
        //resetBranches(); // Limpiar la selección de sucursal

        // Mostrar alerta y comenzar cuenta regresiva
        setShowSuccessAlert(true);
        setRedirectCountdown(10);
    };


    return (
        <div className="space-y-6">
            <h2 className="text-xl font-semibold text-blue-700 pb-2 border-b">Datos de Referencias</h2>

            {/* Alerta de éxito - Adaptable a móviles */}
            {showSuccessAlert ? (
                <div className="flex flex-col items-center justify-center p-4 md:p-8 bg-green-50 border-2 border-green-200 rounded-lg shadow-lg animate-pulse mx-auto max-w-md">
                    <div className="relative">
                        <PartyPopper className="h-12 w-12 md:h-16 md:w-16 text-green-500 mb-2 md:mb-4" />
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full animate-ping"></div>
                    </div>
                    <h3 className="text-xl md:text-2xl font-bold text-green-700 mb-2 text-center">¡Solicitud enviada con éxito!</h3>
                    <p className="text-sm md:text-base text-green-600 text-center mb-3 md:mb-4">
                        Tu solicitud ha sido procesada correctamente.
                    </p>
                    <div className="text-base md:text-xl font-semibold text-green-700 bg-green-100 px-3 py-1 md:px-4 md:py-2 rounded-full flex items-center justify-center">
                        <span className="mr-1">Redirigiendo en</span>
                        <span className="text-xl md:text-2xl mx-1 font-bold">{redirectCountdown}</span>
                        <span>seg</span>
                    </div>
                </div>
            ) : (
                <div className="space-y-6">
                    {references.slice(0, 2).map((ref, index) => (
                        <div
                            key={index}
                            className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4"
                        >
                            <h3 className="text-lg font-medium text-gray-800 flex items-center">
                                <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                                    {index + 1}
                                </span>
                                Referencia Personal #{index + 1}
                            </h3>

                            {/* Campos de la referencia */}
                            <div className="mt-4 px-4 space-y-2">
                                <input
                                    type="text"
                                    placeholder="Nombre completo"
                                    value={ref.fullName}
                                    onChange={(e) => handleFieldChange(index as 0 | 1, "fullName", e.target.value)}
                                    onBlur={() => validateField(index as 0 | 1, "fullName", ref.fullName)}
                                    className={`w-full p-3 border ${errors[index]?.fullName ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                                />
                                {errors[index]?.fullName && (
                                    <p className="text-red-500 text-sm">{errors[index]?.fullName}</p>
                                )}
                            </div>

                            <div className="mt-4 px-4 space-y-2">
                                <input
                                    type="tel"
                                    placeholder="Teléfono"
                                    value={ref.phone}
                                    onChange={(e) => handleFieldChange(index as 0 | 1, "phone", e.target.value)}
                                    onBlur={() => validateField(index as 0 | 1, "phone", ref.phone)}
                                    className={`w-full p-3 border ${errors[index]?.phone ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                                />
                                {errors[index]?.phone && (
                                    <p className="text-red-500 text-sm">{errors[index]?.phone}</p>
                                )}
                            </div>

                            <div className="mt-4 px-4 space-y-2">
                                <input
                                    type="text"
                                    placeholder="Dirección"
                                    value={ref.address}
                                    onChange={(e) => handleFieldChange(index as 0 | 1, "address", e.target.value)}
                                    onBlur={() => validateField(index as 0 | 1, "address", ref.address)}
                                    className={`w-full p-3 border ${errors[index]?.address ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                                />
                                {errors[index]?.address && (
                                    <p className="text-red-500 text-sm">{errors[index]?.address}</p>
                                )}
                            </div>

                            <div className="mt-4 px-4 space-y-2">
                                <select
                                    value={ref.relationship}
                                    onChange={(e) => handleFieldChange(index as 0 | 1, "relationship", e.target.value)}
                                    onBlur={() => validateField(index as 0 | 1, "relationship", ref.relationship)}
                                    className={`w-full p-3 border ${errors[index]?.relationship ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                                >
                                    <option value="">Selecciona parentesco</option>
                                    <option value="Familiar">Familiar</option>
                                    <option value="Amistad">Amistad</option>
                                    <option value="Laboral">Laboral</option>
                                </select>
                                {errors[index]?.relationship && (
                                    <p className="text-red-500 text-sm">{errors[index]?.relationship}</p>
                                )}
                            </div>
                        </div>
                    ))}

                    {/* Referencia del cónyuge (índice 2) */}
                    <div
                        className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4"
                    >
                        <h3 className="text-lg font-medium text-gray-800 flex items-center">
                            <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">
                                <UserPlus className="h-3 w-3" />
                            </span>
                            Referencia del Cónyuge
                        </h3>

                        {/* Campos de la referencia del cónyuge */}
                        <div className="mt-4 px-4 space-y-2">
                            <input
                                type="text"
                                placeholder="Nombre completo del cónyuge"
                                value={references[2].fullName}
                                onChange={(e) => handleFieldChange(2, "fullName", e.target.value)}
                                onBlur={() => validateField(2, "fullName", references[2].fullName)}
                                className={`w-full p-3 border ${errors[2]?.fullName ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                            />
                            {errors[2]?.fullName && (
                                <p className="text-red-500 text-sm">{errors[2]?.fullName}</p>
                            )}
                        </div>

                        <div className="mt-4 px-4 space-y-2">
                            <input
                                type="tel"
                                placeholder="Teléfono del cónyuge"
                                value={references[2].phone}
                                onChange={(e) => handleFieldChange(2, "phone", e.target.value)}
                                onBlur={() => validateField(2, "phone", references[2].phone)}
                                className={`w-full p-3 border ${errors[2]?.phone ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                            />
                            {errors[2]?.phone && (
                                <p className="text-red-500 text-sm">{errors[2]?.phone}</p>
                            )}
                        </div>

                        <div className="mt-4 px-4 space-y-2">
                            <input
                                type="text"
                                placeholder="Dirección del cónyuge"
                                value={references[2].address}
                                onChange={(e) => handleFieldChange(2, "address", e.target.value)}
                                onBlur={() => validateField(2, "address", references[2].address)}
                                className={`w-full p-3 border ${errors[2]?.address ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                            />
                            {errors[2]?.address && (
                                <p className="text-red-500 text-sm">{errors[2]?.address}</p>
                            )}
                        </div>

                        <div className="mt-4 px-4 space-y-2">
                            <input
                                type="text"
                                value="Cónyuge"
                                disabled
                                className="w-full p-3 border border-gray-200 bg-gray-50 rounded-md text-gray-500 text-base cursor-not-allowed"
                            />
                            <p className="text-xs text-gray-500">El parentesco para esta referencia es fijo como "Cónyuge"</p>
                        </div>
                    </div>

                    {/* Sección de firma */}
                    <SignatureSection
                        title="Firma del solicitante"
                        description="Por favor firma para confirmar que toda la información proporcionada en esta solicitud es correcta y verdadera."
                        required={true}
                    />

                    {/* Botones de Navegación */}
                    <div className="flex justify-between pt-6 mt-8 sticky bottom-0 bg-gradient-to-t from-white py-4">
                        {
                            onPrevious ? (
                                <Button
                                    onClick={handlePrevious}
                                    variant="outline"
                                    size="lg"
                                    className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
                                    disabled={isSubmitting}
                                >
                                    Anterior
                                </Button>
                            ) : (
                                <div className="w-[140px]"></div> // Espacio reservado para alineación
                            )
                        }

                        <Button
                            onClick={handleNext}
                            className={`w-full max-w-[140px] ${isSubmitting ? 'bg-blue-400' : isValid ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-300'
                                }`}
                            size="lg"
                            disabled={!isValid || isSubmitting}
                        >
                            {isSubmitting ? (
                                <div className="flex items-center justify-center">
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Enviando
                                </div>
                            ) : (
                                "Finalizar"
                            )}
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
}

