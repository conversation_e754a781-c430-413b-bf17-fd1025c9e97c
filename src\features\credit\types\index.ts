// Credit application types
export interface CreditApplication {
  amount: number;
  term: number;
  nationalId: string;
  deliveryMethod: 'sucursal' | 'cajero';
}

// Common Props
interface StepProps {
  onNext: () => void;
  onPrevious?: () => void;
}

// Credit Amount Step Props
export interface CreditAmountStepProps extends StepProps {
  amount: number;
  setAmount: (amount: number) => void;
}

// Combined Credit Conditions Step Props
export interface CreditConditionsStepProps extends StepProps {
  amount: number;
  setAmount: (amount: number) => void;
  nationalId: string;
  setNationalId: (id: string) => void;
}

// Loan Term Step Props
export interface LoanTermStepProps extends StepProps {
  term: number;
  setTerm: (term: number) => void;
}

// ID Verification Step Props
export interface IdVerificationStepProps extends StepProps {
  nationalId: string;
  setNationalId: (id: string) => void;
}

// Delivery Method Step Props
export interface DeliveryMethodStepProps extends StepProps {
  deliveryMethod: string;
  setDeliveryMethod: (method: string) => void;
  onSubmit: () => void;
}

// Step Indicator Props
export interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

// Complete Credit Application Form Props
export interface CreditApplicationFormProps {
  amount: number;
  setAmount: (amount: number) => void;
  term: number;
  setTerm: (term: number) => void;
  nationalId: string;
  setNationalId: (id: string) => void;
  deliveryMethod: string;
  setDeliveryMethod: (method: string) => void;
  onSubmit: () => void;
}