import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { DELIVERY_METHODS } from "../constants";
import { DeliveryMethodStepProps } from "../types";

/**
 * Step for selecting the delivery method
 */
export function DeliveryMethodStep({
  deliveryMethod,
  setDeliveryMethod,
  onSubmit,
  onPrevious
}: DeliveryMethodStepProps) {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-800">Seleccione la forma de su desembolso</h2>
      <p className="text-gray-600">Elija cómo desea recibir su crédito</p>

      <RadioGroup
        value={deliveryMethod}
        onValueChange={setDeliveryMethod}
        className="space-y-4 mt-6"
      >
        <div className="flex items-start space-x-2 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
          <RadioGroupItem value={DELIVERY_METHODS.BRANCH} id="branch" className="mt-1" />
          <Label htmlFor="branch" className="flex-1 cursor-pointer">
            <div className="font-medium">Sucursal</div>
            <div className="text-sm text-gray-500">Retire su crédito en cualquier sucursal</div>
          </Label>
        </div>
        
        <div className="flex items-start space-x-2 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
          <RadioGroupItem value={DELIVERY_METHODS.ATM} id="atm" className="mt-1" />
          <Label htmlFor="atm" className="flex-1 cursor-pointer">
            <div className="font-medium">Cajero</div>
            <div className="text-sm text-gray-500">Retire su crédito en cualquier cajero automático</div>
          </Label>
        </div>
      </RadioGroup>

      <div className="flex justify-between mt-8">
        <Button
          onClick={onPrevious}
          variant="outline"
          className="w-full max-w-[120px]"
        >
          Anterior
        </Button>
        <Button
          onClick={onSubmit}
          className="w-full max-w-[200px]"
          size="lg"
        >
          Enviar Solicitud
        </Button>
      </div>
    </div>
  );
}