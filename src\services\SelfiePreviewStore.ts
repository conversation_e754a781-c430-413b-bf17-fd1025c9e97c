// src/services/SelfiePreviewStore.ts
class SelfiePreviewStore {
    private videos: Map<string, { url: string, processId: string, timestamp: number }> = new Map();
    
    addVideo(processId: string, url: string): string {
      const previewId = `${processId}-${Date.now()}`;
      this.videos.set(previewId, {
        url,
        processId,
        timestamp: Date.now()
      });
      
      // Clean up old videos (older than 10 minutes)
      this.cleanup();
      
      return previewId;
    }
    
    getVideo(previewId: string) {
      return this.videos.get(previewId);
    }
    
    removeVideo(previewId: string) {
      const video = this.videos.get(previewId);
      if (video) {
        URL.revokeObjectURL(video.url);
        this.videos.delete(previewId);
        return true;
      }
      return false;
    }
    
    private cleanup() {
      const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
      for (const [id, video] of this.videos.entries()) {
        if (video.timestamp < tenMinutesAgo) {
          URL.revokeObjectURL(video.url);
          this.videos.delete(id);
        }
      }
    }
  }
  
  export const selfiePreviewStore = new SelfiePreviewStore();