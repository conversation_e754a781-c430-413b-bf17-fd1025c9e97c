import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  AlertTitle,
  AlertDescription
} from "@/components/ui/alert";
import { AlertCircle, CheckCircle } from "lucide-react";
import { useClientFormStore } from "../types/useClientFormStore";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { useIncomeStore } from "../types/useIncomeStore";
import { personRequestService } from "@/services/PersonByCui";
import { COUNTRY_CODES, DEFAULT_COUNTRY, CountryCode } from "../constants";

// Componente para el selector de código de país
const CountryCodeSelector = ({ 
  value, 
  onChange 
}: { 
  value: CountryCode, 
  onChange: (value: CountryCode) => void 
}) => {
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value as CountryCode)}
      className="p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base w-[140px] flex-shrink-0"
    >
      {Object.entries(COUNTRY_CODES).map(([country, code]) => (
        <option key={country} value={country}>
          +{code} ({country})
        </option>
      ))}
    </select>
  );
};

interface DataClientStepProps {
  cui: string;
  onSubmit: () => Promise<void>;
  onNext: () => void;
  onPrevious?: () => void;
}

export function DataClientStep({
  cui,
  onSubmit,
  onNext,
  onPrevious
}: DataClientStepProps) {
  const {
    name1, name2, name3,
    lastName1, lastName2,
    birth, phone, phone2,
    sexId, marital, education, income,
    setField
  } = useClientFormStore();

  const [validationErrors, setValidationErrors] = useState<Record<string, string | undefined>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitSuccess, setIsSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [countryCode, setCountryCode] = useState<CountryCode>(DEFAULT_COUNTRY);

  const [originalValues, setOriginalValues] = useState({
    name1, name2, name3,
    lastName1, lastName2,
    birth, phone, phone2,
    sexId, marital, education, income
  });

  const [hasChanges, setHasChanges] = useState(false);
  const isFirstMount = useRef(true);

  useEffect(() => {
    if (isFirstMount.current) {
      setOriginalValues({
        name1, name2, name3,
        lastName1, lastName2,
        birth, phone, phone2,
        sexId, marital, education, income
      });
      isFirstMount.current = false;
    }
  }, []);


  useEffect(() => {
    const fetchPersonData = async () => {
      if (!cui) return;

      try {
        const response = await personRequestService.getPersonByCui(cui);

        if (response.success && response.data?.data?.length > 0) {
          const person = response.data.data[0];
          const { setField } = useClientFormStore.getState();

          setField("name1", person.PRIMER_NOMBRE || '');
          setField("name2", person.SEGUNDO_NOMBRE || '');
          setField("name3", '');
          setField("lastName1", person.PRIMER_APELLIDO || '');
          setField("lastName2", person.SEGUNDO_APELLIDO || '');

          const [day, month, year] = person.FECHA_NACIMIENTO.split('/');
          setField("birth", `${year}-${month}-${day}`);

          setField("sexId", person.GENERO === "M" ? 1 : person.GENERO === "F" ? 2 : 0);
          setField("marital", mapEstadoCivil(person.ESTADO_CIVIL));
        }
      } catch (error) {
        console.error("Error al obtener datos del cliente:", error);
      }
    };

    fetchPersonData();
  }, [cui]);

  const mapEstadoCivil = (estado: string): number => {
    switch (estado) {
      case "C": return 1; // Casado
      case "S": return 2; // Soltero
      case "D": return 3; // Divorciado
      case "V": return 4; // Viudo
      case "U": return 5; // Unión de hecho
      default: return 0;
    }
  };

  useEffect(() => {
    if (!isFirstMount.current) {
      const currentValues = {
        name1, name2, name3,
        lastName1, lastName2,
        birth, phone, phone2,
        sexId, marital, education, income
      };

      const changes = Object.keys(currentValues).some(key => {
        // @ts-ignore
        return currentValues[key] !== originalValues[key];
      });

      setHasChanges(changes);
    }
  }, [name1, name2, name3, lastName1, lastName2, birth, phone, phone2, sexId, marital, education, income]);

  const isFormComplete = () => {
    return (
      name1.trim().length > 0 &&
      lastName1.trim().length > 0 &&
      birth !== '' &&
      sexId !== 0 &&
      marital !== 0 &&
      education !== 0 &&
      income !== 0 &&
      phone.length >= 8 &&
      phone.length <= (countryCode === "MX" ? 10 : 8)
    );
  };

  const [isFormValid, setIsFormValid] = useState(false);

  useEffect(() => {
    setIsFormValid(isFormComplete());
  }, [name1, lastName1, birth, sexId, marital, education, income, phone, countryCode]);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const validaPhone = (id: string) => {
    if (!id || id.length === 0) {
      setValidationErrors(prev => ({ ...prev, phone: undefined }));
      return true;
    }

    // Obtener la longitud esperada según el país
    const expectedLength = countryCode === "MX" ? 10 : 8;
    
    if (id.length !== expectedLength || !/^\d+$/.test(id)) {
      setValidationErrors(prev => ({ 
        ...prev, 
        phone: `El número de teléfono debe contener ${expectedLength} dígitos sin el código de país.` 
      }));
      return false;
    }

    setValidationErrors(prev => ({ ...prev, phone: undefined }));
    return true;
  };

  const handlePhoneValidateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    // Limitar la longitud según el país
    const maxLength = countryCode === "MX" ? 10 : 8;
    const trimmedValue = value.slice(0, maxLength);
    setField("phone", trimmedValue);
    validaPhone(trimmedValue);
  };

  const handleCountryCodeChange = (code: CountryCode) => {
    setCountryCode(code);
    // Revalidar el teléfono con el nuevo código de país
    validaPhone(phone);
  };

  const handlePhone2Change = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    // Limitar la longitud según el país
    const maxLength = countryCode === "MX" ? 10 : 8;
    const trimmedValue = value.slice(0, maxLength);
    setField("phone2", trimmedValue);
  };

  const validaNombreApellido = (
    valor: string,
    campo: string,
    esRequerido: boolean = true
  ) => {
    if (!esRequerido && (!valor || valor.trim() === '')) {
      setValidationErrors(prev => ({ ...prev, [campo]: undefined }));
      return true;
    }

    if (esRequerido && (!valor || valor.trim() === '')) {
      setValidationErrors(prev => ({
        ...prev,
        [campo]: `Este campo es obligatorio.`
      }));
      return false;
    }

    if (valor.trim().length < 3) {
      setValidationErrors(prev => ({
        ...prev,
        [campo]: `Debe contener al menos 3 caracteres.`
      }));
      return false;
    }

    if (valor.trim().length > 50) {
      setValidationErrors(prev => ({
        ...prev,
        [campo]: `No puede exceder los 50 caracteres.`
      }));
      return false;
    }

    const nombreRegex = /^[a-zA-ZáéíóúÁÉÍÓÚüÜñÑ\s'-]+$/;
    if (!nombreRegex.test(valor)) {
      setValidationErrors(prev => ({
        ...prev,
        [campo]: `Solo puede contener letras, espacios, guiones y apóstrofes.`
      }));
      return false;
    }

    setValidationErrors(prev => ({ ...prev, [campo]: undefined }));
    return true;
  };

  const handleNameChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    campo: string,
    esRequerido: boolean = true
  ) => {
    const valor = e.target.value;
    const valorFormateado = valor
      .split(' ')
      .map(palabra =>
        palabra.charAt(0).toUpperCase() + palabra.slice(1).toLowerCase()
      )
      .join(' ');

    setField(campo as any, valorFormateado);

    if (valor.length > 1) {
      validaNombreApellido(valorFormateado, campo, esRequerido);
    } else if (validationErrors[campo]) {
      setValidationErrors(prev => ({ ...prev, [campo]: undefined }));
    }
  };

  const validaBirth = (value: string) => {
    if (!value || value.trim() === '') {
      setValidationErrors(prev => ({
        ...prev,
        birth: "La fecha de nacimiento es obligatoria."
      }));
      return false;
    }

    const fechaNacimiento = new Date(value);
    const hoy = new Date();

    if (isNaN(fechaNacimiento.getTime())) {
      setValidationErrors(prev => ({
        ...prev,
        birth: "La fecha ingresada no es válida."
      }));
      return false;
    }

    const edad = hoy.getFullYear() - fechaNacimiento.getFullYear();
    const mesActual = hoy.getMonth() - fechaNacimiento.getMonth();
    const edadAjustada = (mesActual < 0 || (mesActual === 0 && hoy.getDate() < fechaNacimiento.getDate()))
      ? edad - 1
      : edad;

    if (edadAjustada < 18) {
      setValidationErrors(prev => ({
        ...prev,
        birth: "Debes ser mayor de 18 años."
      }));
      return false;
    }

    if (fechaNacimiento > hoy) {
      setValidationErrors(prev => ({
        ...prev,
        birth: "La fecha no puede ser futura."
      }));
      return false;
    }

    if (edadAjustada > 120) {
      setValidationErrors(prev => ({
        ...prev,
        birth: "La edad parece ser demasiado alta, verifica la fecha."
      }));
      return false;
    }

    setValidationErrors(prev => ({ ...prev, birth: undefined }));
    return true;
  };

  const handleBirthChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setField("birth", value);
    validaBirth(value);
  };

  const validaRadioField = (value: number | undefined, fieldName: string, fieldLabel: string) => {
    if (value === undefined || value === 0) {
      setValidationErrors(prev => ({
        ...prev,
        [fieldName]: `Debes seleccionar ${fieldLabel.toLowerCase()}.`
      }));
      return false;
    }

    setValidationErrors(prev => ({ ...prev, [fieldName]: undefined }));
    return true;
  };

  const { setIncomeSourceTypeId } = useIncomeStore();

  const handleRadioChange = (field: string, value: string, label: string) => {
    setField(field as any, Number(value));
    validaRadioField(Number(value), field, label);

    // Si el campo es "income", también actualiza el store global
    if (field === "income") {
      setIncomeSourceTypeId(Number(value));
    }
  };

  const validateForm = () => {
    return (
      validaNombreApellido(name1, "name1", true) &&
      validaNombreApellido(name2, "name2", false) &&
      validaNombreApellido(name3, "name3", false) &&
      validaNombreApellido(lastName1, "lastName1", true) &&
      validaNombreApellido(lastName2, "lastName2", false) &&
      validaBirth(birth) &&
      validaPhone(phone) &&
      validaRadioField(sexId, "sexId", "un género") &&
      validaRadioField(marital, "marital", "un estado civil") &&
      validaRadioField(education, "education", "un nivel educativo") &&
      validaRadioField(income, "income", "un nivel de ingresos")
    );
  };

  const handleNext = async (
    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<HTMLFormElement>
  ) => {
    e.preventDefault();

    const isValid = validateForm();

    if (!isValid) {
      const firstErrorElement = document.querySelector('.border-red-500');
      if (firstErrorElement) {
        firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      const errorCount = Object.values(validationErrors).filter(Boolean).length;

      toast.error('Información incompleta', {
        description: `Por favor complete ${errorCount} ${errorCount === 1 ? 'campo requerido' : 'campos requeridos'} correctamente.`,
      });

      return;
    }

    if (!hasChanges) {
      onNext();
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      await onSubmit();
      setOriginalValues({
        name1, name2, name3,
        lastName1, lastName2,
        birth, phone, phone2,
        sexId, marital, education, income
      });

      setHasChanges(false);
      setIsSubmitSuccess(true);
      onNext();
    } catch (error: any) {
      const message = error?.message || 'Error inesperado. Por favor, intente nuevamente.';
      setSubmitError(message);

      toast.error('Error al enviar', {
        description: message,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrevious = () => {
    if (onPrevious) {
      scrollToTop();
      onPrevious();
    }
  };
  return (
    <div className="space-y-8">
      <h2 className="text-xl font-semibold text-blue-700 pb-2 border-b">Datos Cliente</h2>

      {/* Mensaje de éxito con shadcn/ui Alert */}
      {isSubmitSuccess && (
        <Alert variant="default" className="bg-green-50 border-green-200 text-green-700">
          <CheckCircle className="h-5 w-5 text-green-500" />
          <AlertTitle>Éxito</AlertTitle>
          <AlertDescription>
            ¡Solicitud enviada con éxito! Avanzando al siguiente paso...
          </AlertDescription>
        </Alert>
      )}

      {/* Mensaje de error con shadcn/ui Alert */}
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {submitError}
          </AlertDescription>
        </Alert>
      )}

      {/* Sección de nombres */}
      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">1</span>
          Nombres
        </h3>
        <div className="mt-4 px-4 space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="name1" className="block text-sm font-medium text-gray-700 mb-1">
                Primer nombre <span className="text-red-500">*</span>
              </label>
              <input
                id="name1"
                type="text"
                value={name1}
                onChange={(e) => handleNameChange(e, "name1", true)}
                placeholder="Primer nombre"
                className={`w-full p-3 border ${validationErrors.name1 ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
              />
              {validationErrors.name1 && (
                <p className="text-red-500 text-sm">{validationErrors.name1}</p>
              )}
            </div>
            <div>
              <label htmlFor="name2" className="block text-sm font-medium text-gray-700 mb-1">
                Segundo nombre
              </label>
              <input
                id="name2"
                type="text"
                value={name2}
                onChange={(e) => handleNameChange(e, "name2", false)}
                placeholder="Segundo nombre"
                className={`w-full p-3 border ${validationErrors.name2 ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
              />
              {validationErrors.name2 && (
                <p className="text-red-500 text-sm">{validationErrors.name2}</p>
              )}
            </div>
            <div>
              <label htmlFor="name3" className="block text-sm font-medium text-gray-700 mb-1">
                Tercer nombre
              </label>
              <input
                id="name3"
                type="text"
                value={name3}
                onChange={(e) => handleNameChange(e, "name3", false)}
                placeholder="Tercer nombre"
                className={`w-full p-3 border ${validationErrors.name3 ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
              />
              {validationErrors.name3 && (
                <p className="text-red-500 text-sm">{validationErrors.name3}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Sección de apellidos */}
      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">2</span>
          Apellidos
        </h3>
        <div className="mt-4 px-4 space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="lastName1" className="block text-sm font-medium text-gray-700 mb-1">
                Primer apellido <span className="text-red-500">*</span>
              </label>
              <input
                id="lastName1"
                type="text"
                value={lastName1}
                onChange={(e) => handleNameChange(e, "lastName1", true)}
                placeholder="Primer apellido"
                className={`w-full p-3 border ${validationErrors.lastName1 ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
              />
              {validationErrors.lastName1 && (
                <p className="text-red-500 text-sm">{validationErrors.lastName1}</p>
              )}
            </div>
            <div>
              <label htmlFor="lastName2" className="block text-sm font-medium text-gray-700 mb-1">
                Segundo apellido
              </label>
              <input
                id="lastName2"
                type="text"
                value={lastName2}
                onChange={(e) => handleNameChange(e, "lastName2", true)}
                placeholder="Segundo apellido"
                className={`w-full p-3 border ${validationErrors.lastName2 ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
              />
              {validationErrors.lastName2 && (
                <p className="text-red-500 text-sm">{validationErrors.lastName2}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">3</span>
          Fecha de nacimiento
        </h3>
        <div className="mt-4 px-4 space-y-2">
          <input
            id='birth'
            type="date"
            placeholder="Fecha de nacimiento"
            value={birth}
            onChange={handleBirthChange}
            onBlur={() => validaBirth(birth)}
            max={new Date().toISOString().split('T')[0]}
            className={`w-full p-3 border ${validationErrors.birth ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
            aria-invalid={!!validationErrors.birth}
            aria-describedby={validationErrors.birth ? "birth-error" : "birth-info"}
          />
          {validationErrors.birth && (
            <p className="text-red-500 text-sm">{validationErrors.birth}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">4</span>
          Teléfono personal
        </h3>
        <div className="mt-4 px-4 space-y-2">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
            <CountryCodeSelector value={countryCode} onChange={handleCountryCodeChange} />
            <input
              type="tel"
              placeholder="Teléfono personal"
              value={phone}
              onChange={handlePhoneValidateChange}
              onBlur={() => validaPhone(phone)}
              className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base
                        border-gray-300 focus:border-blue-500"
              style={{ borderColor: validationErrors.phone ? 'rgb(239, 68, 68)' : '' }}
            />
          </div>
          {validationErrors.phone && (
            <p className="text-red-500 text-sm">{validationErrors.phone}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>Ingrese solo los dígitos sin el código de país (+{COUNTRY_CODES[countryCode]})</span>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">5</span>
          Teléfono adicional
        </h3>
        <div className="mt-4 px-4 space-y-2">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
            <CountryCodeSelector value={countryCode} onChange={handleCountryCodeChange} />
            <input
              type="tel"
              placeholder="Teléfono adicional"
              value={phone2}
              onChange={handlePhone2Change}
              className="w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base
                        border-gray-300 focus:border-blue-500"
              style={{ borderColor: validationErrors.phone2 ? 'rgb(239, 68, 68)' : '' }}
            />
          </div>
          {validationErrors.phone2 && (
            <p className="text-red-500 text-sm">{validationErrors.phone2}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span>Opcional: Ingrese solo los dígitos sin el código de país (+{COUNTRY_CODES[countryCode]})</span>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">6</span>
          Género
        </h3>
        <div className="mt-4 px-4 space-y-2">

          <RadioGroup
            value={sexId.toString()}
            onValueChange={(val) => handleRadioChange("sexId", val, "un género")}
            className="flex gap-2"
          >
            <div className="flex items-center space-x-2 flex-1">
              <RadioGroupItem
                value="1"
                id="gender-male"
                className="peer sr-only"
              />
              <Label
                htmlFor="gender-male"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Hombre
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1">
              <RadioGroupItem
                value="2"
                id="gender-female"
                className="peer sr-only"
              />
              <Label
                htmlFor="gender-female"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Mujer
              </Label>
            </div>
          </RadioGroup>

          {validationErrors.phone2 && (
            <p className="text-red-500 text-sm">{validationErrors.phone2}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">7</span>
          Nivel de escolaridad
        </h3>
        <div className="mt-4 px-4 space-y-2">

          <RadioGroup
            value={education.toString()}
            onValueChange={(val) => handleRadioChange("education", val, "un nivel educativo")}
            className="flex flex-wrap gap-2"
          >
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="1"
                id="sinescolaridad"
                className="peer sr-only"
              />
              <Label
                htmlFor="sinescolaridad"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Sin escolaridad
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="2"
                id="primaria"
                className="peer sr-only"
              />
              <Label
                htmlFor="primaria"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">

                Primaria
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="3"
                id="basica"
                className="peer sr-only"
              />
              <Label
                htmlFor="basica"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Básica
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="4"
                id="diversificado"
                className="peer sr-only"
              />
              <Label
                htmlFor="diversificado"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Diversificado
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="5"
                id="tecnico"
                className="peer sr-only"
              />
              <Label
                htmlFor="tecnico"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Técnico
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="6"
                id="postgrado"
                className="peer sr-only"
              />
              <Label
                htmlFor="postgrado"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Post-Grado
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="7"
                id="universitario"
                className="peer sr-only"
              />
              <Label
                htmlFor="universitario"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Universitario
              </Label>
            </div>
          </RadioGroup>

          {validationErrors.marital && (
            <p className="text-red-500 text-sm">{validationErrors.marital}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">8</span>
          Estado civil
        </h3>
        <div className="mt-4 px-4 space-y-2">

          <RadioGroup
            value={marital.toString()}
            onValueChange={(val) => handleRadioChange("marital", val, "un estado civil")}
            className="flex flex-wrap gap-2"
          >
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="1"
                id="casado"
                className="peer sr-only"
              />
              <Label
                htmlFor="casado"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Casado
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="2"
                id="soltero"
                className="peer sr-only"
              />
              <Label
                htmlFor="soltero"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">

                Soltero
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="3"
                id="divorciado"
                className="peer sr-only"
              />
              <Label
                htmlFor="divorciado"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Divorciado
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="4"
                id="viudo"
                className="peer sr-only"
              />
              <Label
                htmlFor="viudo"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Viudo
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
              <RadioGroupItem
                value="5"
                id="unionhecho"
                className="peer sr-only"
              />
              <Label
                htmlFor="unionhecho"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Unión de hecho
              </Label>
            </div>
          </RadioGroup>

          {validationErrors.marital && (
            <p className="text-red-500 text-sm">{validationErrors.marital}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>

      <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">9</span>
          Principal fuente de ingresos
        </h3>
        <div className="mt-4 px-4 space-y-2">

          <RadioGroup
            value={income.toString()}
            onValueChange={(val) => handleRadioChange("income", val, "un nivel de ingresos")} className="flex gap-2"
          >
            <div className="flex items-center space-x-2 flex-1">
              <RadioGroupItem
                value="1"
                id="negocio"
                className="peer sr-only"
              />
              <Label
                htmlFor="negocio"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Negocio
              </Label>
            </div>
            <div className="flex items-center space-x-2 flex-1">
              <RadioGroupItem
                value="2"
                id="asalariado"
                className="peer sr-only"
              />
              <Label
                htmlFor="asalariado"
                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer 
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white 
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50 
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all 
                  text-center flex items-center justify-center">
                Asalariado
              </Label>
            </div>
          </RadioGroup>

          {validationErrors.income && (
            <p className="text-red-500 text-sm">{validationErrors.income}</p>
          )}
          <div className="flex items-center text-xs text-gray-500 mt-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1 text-blue-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        </div>
      </div>



      {/* Botones de Navegación */}
      <div className="flex justify-between pt-6 mt-8 py-4">
        {onPrevious ? (
          <Button
            onClick={handlePrevious}
            variant="outline"
            size="lg"
            className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
            disabled={isSubmitting}
          >
            Anterior
          </Button>
        ) : (
          <div className="w-[140px]"></div> // Espacio reservado para alineación
        )}

        <Button
          onClick={handleNext}
          className={`w-full max-w-[140px] ${isSubmitting ? 'bg-blue-400' : isFormValid ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-300'
            }`}
          size="lg"
          disabled={!isFormValid || isSubmitting}
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Enviando
            </div>
          ) : (
            "Siguiente"
          )}
        </Button>
      </div>
    </div>
  );
}
