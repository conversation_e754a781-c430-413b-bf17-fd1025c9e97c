
import { Reference } from "@/features/credit/types/useReferencesFormStore";

export function validateReferences(references: Reference[]): {
  isValid: boolean;
  errors: Array<Record<keyof Reference, string | null>>;
} {
  const errors = references.map((ref) => {
    const refErrors: Record<keyof Reference, string | null> = {
      fullName: null,
      phone: null,
      address: null,
      relationship: null,
    };

    // Validación de nombre completo
    if (!ref.fullName.trim()) {
      refErrors.fullName = "El nombre es obligatorio.";
    } else if (ref.fullName.trim().length < 3) {
      refErrors.fullName = "Debe tener al menos 3 caracteres.";
    }

    // Validación de teléfono
    const phoneDigits = ref.phone.replace(/\D/g, "");
    if (!phoneDigits || phoneDigits.length < 8) {
      refErrors.phone = "Debe contener al menos 8 dígitos numéricos.";
    }

    // Validación de dirección
    if (!ref.address.trim()) {
      refErrors.address = "La dirección es obligatoria.";
    }

    // Validación de parentesco
    if (!ref.relationship || !["Familiar", "Amistad", "Laboral"].includes(ref.relationship)) {
      refErrors.relationship = "Selecciona un parentesco válido.";
    }

    return refErrors;
  });

  const isValid = errors.every((ref) =>
    Object.values(ref).every((value) => value === null)
  );

  return { isValid, errors };
}
