import { create } from "zustand";

interface ClientFormState {
  name1: string;
  name2: string;
  name3: string;
  lastName1: string;
  lastName2: string;
  birth: string;
  phone: string;
  phone2: string;
  sexId: number;
  marital: number;
  education: number;
  income: number;

  setField: <K extends keyof Omit<ClientFormState, 'setField' | 'reset'>>(
    field: K,
    value: ClientFormState[K]
  ) => void;

  reset: () => void;
}

const initialState: Omit<ClientFormState, 'setField' | 'reset'> = {
  name1: '',
  name2: '',
  name3: '',
  lastName1: '',
  lastName2: '',
  birth: '',
  phone: '',
  phone2: '',
  sexId: 0,
  marital: 0,
  education: 0,
  income: 0,
};

export const useClientFormStore = create<ClientFormState>((set) => ({
  ...initialState,

  setField: (field, value) =>
    set((state) => ({
      ...state,
      [field]: value,
    })),

  reset: () => set(() => ({ ...initialState })),
}));
