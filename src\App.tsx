import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Layout from "./components/Layout/Layout";
import WelcomePage from "./pages/welcome/WelcomePage";
import TermsPage from "./pages/terms/TermsPage";
import ApplicationPage from "./pages/application/ApplicationPage";

import "./App.css";

function App() {
  return (
    <BrowserRouter>
      <Routes>

        
        {/* Main application routes with layout */}
        <Route path="/" element={<Layout />}>
          <Route index element={<WelcomePage />} />
          <Route path="terms" element={<TermsPage />} />
          <Route path="application" element={<ApplicationPage />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;