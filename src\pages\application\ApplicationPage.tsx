import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  StepIndicator,
  CreditApplicationForm,
  DataClientStep,
  DataResidenceStep,
  DataLaboralStep,
  DataNegocioStep,
  DataReferenciasStep,
  CREDIT_AMOUNT,
  LOAN_TERM,
  DELIVERY_METHODS
} from "@/features/credit";
import { useVerificationStore } from "@/stores";
import { loanRequestService } from "@/services";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useClientFormStore } from "@/features/credit/types/useClientFormStore";
import { useResidenceFormStore } from "@/features/credit/types/useResidenceFormStore";
import { useWorkFormStore } from "@/features/credit/types/useWorkFormStore";
import { useReferencesFormStore } from "@/features/credit/types/useReferencesFormStore";
import { useIncomeStore } from "@/features/credit/types/useIncomeStore";

/**
 * Componente principal del formulario de solicitud
 */
export default function ApplicationPage() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);

  // Use verification store for national ID
  const { nationalId, setNationalId, error } = useVerificationStore();

  // Add local loading state instead of using the one from the store
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Estado del formulario (mantiene el resto de campos localmente)
  const [amount, setAmount] = useState(CREDIT_AMOUNT.DEFAULT);
  const [term, setTerm] = useState(LOAN_TERM.DEFAULT);
  const [deliveryMethod, setDeliveryMethod] = useState(DELIVERY_METHODS.BRANCH);


  // Estado para el seguimiento del ID de solicitud
  const [loanRequestId, setLoanRequestId] = useState<string | null>(null);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  // Desplazar al inicio al cambiar de paso
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [currentStep]);

  // Avanzar al siguiente paso
  const nextStep = () => {
    setCurrentStep(current => Math.min(current + 1, 5)); // 5 es el máximo (6to paso)
  };

  // Retroceder al paso anterior
  const prevStep = () => setCurrentStep(current => Math.max(current - 1, 0));

  // Enviar datos del primer paso
  const submitStep1 = async () => {
    try {
      setIsSubmitting(true);
      setSubmissionError(null);

      // Preparar datos para el servicio
      const loanRequestData = {
        requestedAmount: amount,
        loanTerm: term,
        identificationNumber: nationalId,
        withdrawalMethodId: deliveryMethod === DELIVERY_METHODS.BRANCH ? 1 : 2
      };
      console.log(loanRequestData);
      // Enviar al API
      // descomentar luego
      const response = await loanRequestService.submitStep1(loanRequestData);

      if (response.success && response.data) {
        if (response.data.clientId) {
          setLoanRequestId(String(response.data.clientId));
          localStorage.setItem("loanRequestId", response.data.clientId.toString());
        }
        // Mostrar mensaje de éxito solamente con toast
        toast.success('Datos guardados', {
          description: "La información de crédito se ha guardado correctamente"
        });

        // Avanzar al siguiente paso
        nextStep();
      } else {
        setSubmissionError(response.error || 'Error al enviar la solicitud. Por favor, intente nuevamente.');
        toast.error('Error', {
          description: response.error || 'Error al enviar la solicitud. Por favor, intente nuevamente.'
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Error inesperado. Por favor, intente nuevamente.';
      setSubmissionError(errorMessage);
      toast.error('Error', {
        description: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitStep2 = async () => {
    console.log('ingreso al step 2')
    try {
      setIsSubmitting(true);
      setSubmissionError(null);

      const loanRequestId = localStorage.getItem("loanRequestId");
      if (!loanRequestId) {
        throw new Error("No se encontró el ID de solicitud.");
      }


      const {
        name1,
        name2,
        name3,
        lastName1,
        lastName2,
        birth,
        phone,
        phone2,
        sexId,
        marital,
        education,
        income,
      } = useClientFormStore.getState();

      const loanRequestData = {
        loanRequestId, // O el ID real de la solicitud
        name1,
        name2,
        name3,
        lastName1,
        lastName2,
        birth,
        phone,
        phone2,
        sexId,
        marital,
        education,
        income,
      };

      console.log('loanRequestData2: ', loanRequestData);

      // Enviar al API
      // descomentar luego
      const response = await loanRequestService.submitStep2(loanRequestData);

      // const response = {
      //   success: true,
      //   data: { /* tus datos aquí */ },
      //   error: null
      // };

      if (response.success && response.data) {
        // Guardar el ID de la solicitud para pasos futuros
        // descomentar luego
        if (response.data.clientId) {
          setLoanRequestId(response.data.clientId.toString());
        }

        // Mostrar mensaje de éxito solamente con toast
        toast.success('Datos guardados', {
          description: "La información de crédito se ha guardado correctamente"
        });

        // Avanzar al siguiente paso
        nextStep();
      } else {
        setSubmissionError(response.error || 'Error al enviar la solicitud. Por favor, intente nuevamente.');
        toast.error('Error', {
          description: response.error || 'Error al enviar la solicitud. Por favor, intente nuevamente.'
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Error inesperado. Por favor, intente nuevamente.';
      setSubmissionError(errorMessage);
      toast.error('Error', {
        description: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitStepResidence = async () => {
    try {

      const loanRequestId = localStorage.getItem("loanRequestId");
      if (!loanRequestId) {
        throw new Error("No se encontró el ID de solicitud.");
      }

      const {
        residenceType,
        departmentId,
        municipalityId,
        address,
        livingTime,
        isPep,
        isCpe,
        dependents,
        vehicleType,
      } = useResidenceFormStore.getState();

      const requestData = {
        loanRequestId,
        residenceType: residenceType ?? 0,
        departmentId: departmentId ?? 0,
        municipalityId: municipalityId ?? 0,
        address,
        livingTime: livingTime ?? 0,
        isPep: isPep ?? 0,
        isCpe: isCpe ?? 0,
        dependents: dependents ?? 0,
        vehicleType: vehicleType ?? 0,
      };

      console.log("📤 Enviando datos de residencia:", requestData);

      // Aquí iría tu llamado real a la API, como:
      const response = await loanRequestService.submitStepResidence(requestData);

      //const response = { success: true, data: {}, error: null };

      if (response.success) {
        if (response.data!.clientId) {
          setLoanRequestId(response.data!.clientId.toString());
        }

        toast.success("Datos de residencia guardados correctamente.");
      } else {
        throw new Error(response.error || "Ocurrió un error inesperado.");
      }
    } catch (error: any) {
      const message = error?.message || "Error inesperado al guardar los datos.";
      toast.error("Error al enviar", { description: message });
      throw new Error(message); // para manejarlo desde el componente si es necesario
    }
  };

  const submitStepLaboral = async (): Promise<void> => {
    try {

      const loanRequestId = localStorage.getItem("loanRequestId");
      if (!loanRequestId) {
        throw new Error("No se encontró el ID de solicitud.");
      }

      const {
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId,
        companyMunicipalityId,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount,
        laborLetterFile,
        electricBillFile,
      } = useWorkFormStore.getState();

      if (!laborLetterFile || !electricBillFile) {
        throw new Error("Debes adjuntar ambos archivos requeridos.");
      }

      const requestData = {
        loanRequestId,
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId: companyDepartmentId ?? 0,
        companyMunicipalityId: companyMunicipalityId ?? 0,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount: hasBankAccount === 1 ? "true" : "false", // string
        attachments: [
          {
            file: laborLetterFile,
            attachmentTypeId: 5, // Carta laboral
          },
          {
            file: electricBillFile,
            attachmentTypeId: 10, // Recibo de luz
          },
        ],
      };

      console.log("📤 Enviando datos laborales:", requestData);

      const response = await loanRequestService.submitStepLaboral(requestData);

      if (response.success) {
        if (response.data?.clientId) {
          setLoanRequestId(response.data.clientId.toString());
        }

        toast.success("Datos laborales guardados correctamente.");
      } else {
        throw new Error(response.error || "Ocurrió un error inesperado.");
      }
    } catch (error: any) {
      const message = error?.message || "Error inesperado al guardar los datos.";
      toast.error("Error al enviar", { description: message });
      throw new Error(message);
    }
  };

  const submitStepNegocio = async (): Promise<void> => {
    try {

      const loanRequestId = localStorage.getItem("loanRequestId");
      if (!loanRequestId) {
        throw new Error("No se encontró el ID de solicitud.");
      }

      const {
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId,
        companyMunicipalityId,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount,
        laborLetterFile,
        electricBillFile,
        nit,
        economicActivityId,
        businessOwnershipTypeId,
        address,
      } = useWorkFormStore.getState();

      if (!laborLetterFile || !electricBillFile) {
        throw new Error("Debes adjuntar ambos archivos requeridos.");
      }

      const requestData = {
        loanRequestId,
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId: companyDepartmentId ?? 0,
        companyMunicipalityId: companyMunicipalityId ?? 0,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount: hasBankAccount === 1 ? "true" : "false", // string
        nit,
        economicActivityId,
        businessOwnershipTypeId,
        address,
        attachments: [
          {
            file: laborLetterFile,
            attachmentTypeId: 4, // negocio
          },
          {
            file: electricBillFile,
            attachmentTypeId: 10, // Recibo de luz
          },
        ],
      };

      console.log("📤 Enviando datos laborales:", requestData);

      const response = await loanRequestService.submitStepLaboral(requestData);

      if (response.success) {
        if (response.data?.clientId) {
          setLoanRequestId(response.data.clientId.toString());
        }

        toast.success("Datos laborales guardados correctamente.");
      } else {
        throw new Error(response.error || "Ocurrió un error inesperado.");
      }
    } catch (error: any) {
      const message = error?.message || "Error inesperado al guardar los datos.";
      toast.error("Error al enviar", { description: message });
      throw new Error(message);
    }
  };

  const submitStepReferencias = async (): Promise<void> => {
    try {
      const { references } = useReferencesFormStore.getState();

      const loanRequestId = localStorage.getItem("loanRequestId");

      if (!loanRequestId) {
        throw new Error("No se encontró el ID de solicitud.");
      }

      const requestData = {
        clientId: Number(loanRequestId),
        referers: references.map(ref => ({
          fullName: ref.fullName,
          phoneNumber: ref.phone,
          address: ref.address,
          kihsipId: parentescoToKihsipId(ref.relationship),
        })),
      };

      console.log("📤 Enviando referencias:", requestData);

      const response = await loanRequestService.submitStepReferencias(requestData);

      if (response.success) {
        toast.success("Referencias guardadas correctamente.");
      } else {
        throw new Error(response.error || "Ocurrió un error inesperado.");
      }
    } catch (error: any) {
      const message = error?.message || "Error inesperado al guardar las referencias.";
      toast.error("Error al enviar referencias", { description: message });
      throw new Error(message);
    }
  };

  // Conversión de relación string a ID esperado por el backend
  const parentescoToKihsipId = (rel: string): number => {
    switch (rel) {
      case "Familiar": return 1;
      case "Amistad": return 2;
      case "Laboral": return 3;
      default: return 0; // o lanza un error si es requerido
    }
  };

  // Manejar el envío final del formulario
  const handleFinalSubmit = async () => {
    if (!loanRequestId) {
      setSubmissionError('Error en la solicitud. Falta ID de solicitud.');
      toast.error('Error', {
        description: "Error en la solicitud. Falta ID de solicitud."
      });
      return;
    }

    try {
      setIsSubmitting(true);
      setSubmissionError(null);

      // Enviar datos financieros (ejemplo)

      // Aquí podrías usar otro servicio específico para el paso final
      const response = await loanRequestService.submitStep6({ loanRequestId });

      if (response.success) {
        // Mostrar mensaje de éxito con Sonner
        toast.success('¡Solicitud completada!', {
          description: "Su solicitud de crédito ha sido procesada correctamente"
        });

        // Redireccionar después de un breve periodo
        setTimeout(() => {
          navigate('/');
        }, 2000);
      } else {
        setSubmissionError(response.error || 'Error al finalizar la solicitud.');
        toast.error('Error', {
          description: response.error || 'Error al finalizar la solicitud.'
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || 'Error al finalizar la solicitud.';
      setSubmissionError(errorMessage);
      toast.error('Error', {
        description: errorMessage
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Función para renderizar el paso actual
  const renderStep = () => {
    const { incomeSourceTypeId } = useIncomeStore();

    switch (currentStep) {
      case 0:
        return (
          <CreditApplicationForm
            amount={amount}
            setAmount={setAmount}
            term={term}
            setTerm={setTerm}
            nationalId={nationalId}
            setNationalId={setNationalId}
            deliveryMethod={deliveryMethod}
            setDeliveryMethod={setDeliveryMethod}
            onSubmit={submitStep1} // Ahora enviamos los datos del paso 1 aquí
            onNext={nextStep}
          />
        );
      case 1:
        // Paso "Datos Cliente"
        return (
          <DataClientStep
            cui={nationalId}
            onSubmit={submitStep2}
            onNext={nextStep}
            onPrevious={prevStep}
          />);
      // case 2:
      //   // Usar el componente PhotographsStep
      //   return (
      //     <PhotographsStep
      //       onSubmit={nextStep}
      //       onPrevious={prevStep}
      //     />
      //   );
      case 2:
        // Paso "Datos Financieros"
        return (
          <DataResidenceStep
            onSubmit={submitStepResidence}
            onNext={nextStep}
            onPrevious={prevStep}
          />
        );
      case 3:
        // Paso "Datos Financieros"
        return (
          <>
            {incomeSourceTypeId === 1 &&
            <DataNegocioStep
                onSubmit={submitStepLaboral}
                onNext={nextStep}
                onPrevious={prevStep}
              />
            }
            {incomeSourceTypeId === 2 &&
              <DataLaboralStep
                onSubmit={submitStepLaboral}
                onNext={nextStep}
                onPrevious={prevStep}
              />
            }
          </>
        );
      case 4:
        // Paso "Referencias"
        return (
          <DataReferenciasStep
            onSubmit={submitStepReferencias}
            onNext={nextStep}
            onPrevious={prevStep}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col min-h-screen p-4 md:p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Solicitud de Crédito</h1>

      {/* Indicador de pasos */}
      <StepIndicator currentStep={currentStep} totalSteps={5} />

      <div className="bg-gray-50 p-4 md:p-6 rounded-lg shadow-md">
        {/* Only show error message if needed, no success alerts */}
        {(error || submissionError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-5 w-5" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error || submissionError}</AlertDescription>
          </Alert>
        )}

        {renderStep()}
      </div>

      {/* Indicador flotante del progreso */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white px-4 py-2 rounded-full shadow-lg border border-gray-200 text-sm font-medium text-gray-700">
        Paso {currentStep + 1} de 5
      </div>
    </div>
  );
}