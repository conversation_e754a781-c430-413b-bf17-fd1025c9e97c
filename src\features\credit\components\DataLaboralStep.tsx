import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import {
    Alert,
    AlertTitle,
    AlertDescription
} from "@/components/ui/alert";
import { AlertCircle, CheckCircle } from "lucide-react";
import { useWorkFormStore, WorkFormState } from "../types/useWorkFormStore";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { formatCurrency } from "@/lib/utils";
import { CREDIT_AMOUNT } from "../constants";

interface DataLaboralStepProps {
    onSubmit: () => Promise<void>;
    onNext: () => void;
    onPrevious?: () => void;
}

export function DataLaboralStep({
    onSubmit,
    onNext,
    onPrevious
}: DataLaboralStepProps) {
    const {
        companyName,         // 1. Nombre de la empresa
        companyEntryDate,    // 2. Fecha de ingreso a la empresa
        companyPhone,        // 3. Teléfono de la empresa
        companyDepartmentId, // 4. Departamento de la empresa
        companyMunicipalityId, // 5. Municipio de la empresa
        monthlySalary,       // 6. Salario mensual
        monthlyExpenses,     // 7. Gastos mensuales
        otherIncome,         // 8. Otros ingresos
        remittanceIncome,    // 9. Ingresos por remesas
        hasBankAccount,      // 10. Tiene cuenta bancaria (boolean o number)
        laborLetterFile,     // 11. Foto de carta laboral (string | File | null)
        electricBillFile,     // 12. Fotografía del recibo de luz (string | File | null)
        setField
    } = useWorkFormStore();

    const [validationErrors, setValidationErrors] = useState<Record<string, string | undefined>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitSuccess, setIsSubmitSuccess] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);

    // Objeto para rastrear con qué campos ha interactuado el usuario
    const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});

    const [originalValues, setOriginalValues] = useState({
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId,
        companyMunicipalityId,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount,
        laborLetterFile,
        electricBillFile
    });

    const [hasChanges, setHasChanges] = useState(false);
    const isFirstMount = useRef(true);

    useEffect(() => {
        if (isFirstMount.current) {
            setOriginalValues({
                companyName,
                companyEntryDate,
                companyPhone,
                companyDepartmentId,
                companyMunicipalityId,
                monthlySalary,
                monthlyExpenses,
                otherIncome,
                remittanceIncome,
                hasBankAccount,
                laborLetterFile,
                electricBillFile,
            });
            isFirstMount.current = false;
        }
    }, []);

    useEffect(() => {
        if (!isFirstMount.current) {
            const currentValues = {
                companyName,
                companyEntryDate,
                companyPhone,
                companyDepartmentId,
                companyMunicipalityId,
                monthlySalary,
                monthlyExpenses,
                otherIncome,
                remittanceIncome,
                hasBankAccount,
                laborLetterFile,
                electricBillFile,
            };

            const changes = Object.keys(currentValues).some(key => {
                // @ts-ignore
                return currentValues[key] !== originalValues[key];
            });

            setHasChanges(changes);
        }
    }, [
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId,
        companyMunicipalityId,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount,
        laborLetterFile,
        electricBillFile
    ]);

    const [isFormValid, setIsFormValid] = useState(false);
    const [isEditingAmount, setIsEditingAmount] = useState(false);
    const [isEditingMonthlySalary, setIsEditingMonthlySalary] = useState(false);
    const [monthlySalaryValue, setMonthlySalaryValue] = useState(monthlySalary.toString());

    const [isEditingMonthlyExpenses, setIsEditingMonthlyExpenses] = useState(false);
    const [monthlyExpensesValue, setMonthlyExpensesValue] = useState(monthlyExpenses.toString());

    const [isEditingOtherIncome, setIsEditingOtherIncome] = useState(false);
    const [otherIncomeValue, setOtherIncomeValue] = useState(otherIncome.toString());

    const [isEditingRemittanceIncome, setIsEditingRemittanceIncome] = useState(false);
    const [remittanceIncomeValue, setRemittanceIncomeValue] = useState(remittanceIncome.toString());

    const [departments, setDepartments] = useState<{ id: number; name: string }[]>([]);
    const [municipalities, setMunicipalities] = useState<{ id: number; name: string }[]>([]);


    const handleFieldClick = (
        setIsEditing: React.Dispatch<React.SetStateAction<boolean>>,
        setValue: React.Dispatch<React.SetStateAction<string>>,
        currentValue: number
    ) => {
        setIsEditing(true);
        setValue(currentValue.toString());
    };


    const handleFieldChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        setValue: React.Dispatch<React.SetStateAction<string>>
    ) => {
        setValue(e.target.value);
    };

    type WorkFormField = keyof Omit<WorkFormState, 'setField' | 'reset'>;


    const handleFieldBlur = (
        valueStr: string,
        field: WorkFormField,
        setValue: React.Dispatch<React.SetStateAction<string>>,
        setIsEditing: React.Dispatch<React.SetStateAction<boolean>>,
        min: number = 0,
        max: number = 50000
    ) => {
        const value = parseInt(valueStr);
        if (!isNaN(value) && value >= min && value <= max) {
            setField(field, value);
            setValidationErrors(prev => ({ ...prev, [field]: undefined }));
        } else {
            setValidationErrors(prev => ({
                ...prev,
                [field]: `Debe estar entre ${formatCurrency(min)} y ${formatCurrency(max)}.`
            }));
            setValue('0');
        }
        setIsEditing(false);
    };

    const handleFieldKeyDown = (
        e: React.KeyboardEvent<HTMLInputElement>,
        onBlur: () => void
    ) => {
        if (e.key === 'Enter') {
            onBlur();
        }
    };


    useEffect(() => {
        // Validamos el formulario sin mostrar errores para campos no tocados
        const isValid = validateForm(false);
        setIsFormValid(isValid);
    }, [
        companyName,
        companyEntryDate,
        companyPhone,
        companyDepartmentId,
        companyMunicipalityId,
        monthlySalary,
        monthlyExpenses,
        otherIncome,
        remittanceIncome,
        hasBankAccount,
        laborLetterFile,
        electricBillFile,
        touchedFields // Agregamos touchedFields para que se actualice cuando cambie
    ]);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    useEffect(() => {
        // Simular carga de departamentos desde API
        setDepartments([
            { id: 1, name: "Departamento A" },
            { id: 2, name: "Departamento B" }
        ]);
    }, []);

    useEffect(() => {
        if (companyDepartmentId) {
            setMunicipalities([
                { id: 1, name: "Municipio X" },
                { id: 2, name: "Municipio Y" }
            ]);
        } else {
            setMunicipalities([]);
        }
    }, [companyDepartmentId]);



    const validaCompanyName = (valor: string, showError = touchedFields.companyName) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        if (!valor || valor.trim() === '') {
            errorMessage = "Este campo es obligatorio.";
            isValid = false;
        } else if (valor.trim().length < 3) {
            errorMessage = "Debe tener al menos 3 caracteres.";
            isValid = false;
        } else if (valor.trim().length > 100) {
            errorMessage = "No puede superar los 100 caracteres.";
            isValid = false;
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, companyName: errorMessage }));
        }

        return isValid;
    };

    const handleCompanyNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setField("companyName", value);
        setTouchedFields(prev => ({ ...prev, companyName: true }));
        validaCompanyName(value, true);
    };

    const validaCompanyEntryDate = (value: string, showError = touchedFields.companyEntryDate) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        if (!value || value.trim() === '') {
            errorMessage = "Este campo es obligatorio.";
            isValid = false;
        } else {
            const fecha = new Date(value);
            const hoy = new Date();

            if (isNaN(fecha.getTime())) {
                errorMessage = "Fecha no válida.";
                isValid = false;
            } else if (fecha > hoy) {
                errorMessage = "No puede ser una fecha futura.";
                isValid = false;
            }
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, companyEntryDate: errorMessage }));
        }

        return isValid;
    };

    const handleMonthlySalaryClick = () => {
        setIsEditingMonthlySalary(true);
        setMonthlySalaryValue(monthlySalary.toString());
    };

    const handleMonthlySalaryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setMonthlySalaryValue(e.target.value);
    };

    const handleMonthlySalaryBlur = () => {
        const newSalary = parseInt(monthlySalaryValue);
        if (!isNaN(newSalary) && newSalary >= CREDIT_AMOUNT.MIN && newSalary <= CREDIT_AMOUNT.MAX) {
            setField("monthlySalary", newSalary);
            setValidationErrors(prev => ({ ...prev, monthlySalary: undefined }));
        } else {
            setMonthlySalaryValue(monthlySalary.toString());
            setValidationErrors(prev => ({
                ...prev,
                monthlySalary: `El salario debe estar entre ${formatCurrency(CREDIT_AMOUNT.MIN)} y ${formatCurrency(CREDIT_AMOUNT.MAX)}.`
            }));
        }
        setIsEditingMonthlySalary(false);
    };

    const handleMonthlySalaryKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === "Enter") {
            handleMonthlySalaryBlur();
        }
    };

    const handleCompanyEntryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setField("companyEntryDate", value);
        setTouchedFields(prev => ({ ...prev, companyEntryDate: true }));
        validaCompanyEntryDate(value, true);
    };

    const validaCompanyPhone = (value: string, showError = touchedFields.companyPhone) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        const cleaned = value.replace(/[^0-9]/g, '');
        if (cleaned.length < 8 || cleaned.length > 11) {
            errorMessage = "Debe tener entre 8 y 11 dígitos.";
            isValid = false;
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, companyPhone: errorMessage }));
        }

        return isValid;
    };

    const handleCompanyPhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/[^0-9]/g, '').slice(0, 11);
        setField("companyPhone", value);
        setTouchedFields(prev => ({ ...prev, companyPhone: true }));
        validaCompanyPhone(value, true);
    };

    const validaPositiveAmount = (value: number, campo: string, requerido = true, showError = touchedFields[campo]) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        if (requerido && (value === null || isNaN(value) || value === 0)) {
            errorMessage = "Este campo es obligatorio.";
            isValid = false;
        } else if (value < 0) {
            errorMessage = "No puede ser negativo.";
            isValid = false;
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, [campo]: errorMessage }));
        }

        return isValid;
    };


    const validaBankAccount = (valor: number | null, showError = touchedFields.hasBankAccount) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        if (valor === null) {
            errorMessage = "Debe seleccionar una opción.";
            isValid = false;
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, hasBankAccount: errorMessage }));
        }

        return isValid;
    };

    const validaFile = (file: File | null | string, campo: string, showError = touchedFields[campo]) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        if (!file) {
            errorMessage = "Debe adjuntar un archivo.";
            isValid = false;
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, [campo]: errorMessage }));
        }

        return isValid;
    };

    const [hasInteracted, setHasInteracted] = useState(false);


    const handleFieldChange2 = <K extends keyof Omit<WorkFormState, 'setField' | 'reset'>>(
        field: K,
        value: WorkFormState[K]
    ) => {
        setField(field, value);
        if (!hasInteracted) setHasInteracted(true);
    };



    const validaRadioField = (value: number | undefined, fieldName: string, fieldLabel: string, showError = touchedFields[fieldName]) => {
        let isValid = true;
        let errorMessage: string | undefined = undefined;

        if (value === undefined || value === 0) {
            errorMessage = `Debes seleccionar ${fieldLabel.toLowerCase()}.`;
            isValid = false;
        }

        // Solo actualiza el error si debemos mostrarlo
        if (showError) {
            setValidationErrors(prev => ({ ...prev, [fieldName]: errorMessage }));
        }

        return isValid;
    };

    const handleRadioChange = (field: string, value: string, label: string) => {
        setField(field as any, Number(value));
        setTouchedFields(prev => ({ ...prev, [field]: true }));
        validaRadioField(Number(value), field, label, true);
    };

    const validateForm = (showAllErrors = false) => {
        // Si showAllErrors es true, forzamos la visualización de todos los errores
        // como si todos los campos hubieran sido tocados

        // Check each validation individually and log the results
        const companyNameValid = validaCompanyName(companyName, showAllErrors || touchedFields.companyName);
        const companyEntryDateValid = validaCompanyEntryDate(companyEntryDate, showAllErrors || touchedFields.companyEntryDate);
        const companyPhoneValid = validaCompanyPhone(companyPhone, showAllErrors || touchedFields.companyPhone);
        const departmentValid = validaRadioField(
            companyDepartmentId ?? undefined,
            "companyDepartmentId",
            "un departamento",
            showAllErrors || touchedFields.companyDepartmentId
        );
        const municipalityValid = validaRadioField(
            companyMunicipalityId ?? undefined,
            "companyMunicipalityId",
            "un municipio",
            showAllErrors || touchedFields.companyMunicipalityId
        );
        const monthlySalaryValid = validaPositiveAmount(
            monthlySalary,
            "monthlySalary",
            true,
            showAllErrors || touchedFields.monthlySalary
        );
        const monthlyExpensesValid = validaPositiveAmount(
            monthlyExpenses,
            "monthlyExpenses",
            true,
            showAllErrors || touchedFields.monthlyExpenses
        );
        const otherIncomeValid = validaPositiveAmount(
            otherIncome,
            "otherIncome",
            false,
            showAllErrors || touchedFields.otherIncome
        );
        const remittanceIncomeValid = validaPositiveAmount(
            remittanceIncome,
            "remittanceIncome",
            false,
            showAllErrors || touchedFields.remittanceIncome
        );
        const bankAccountValid = validaBankAccount(
            hasBankAccount,
            showAllErrors || touchedFields.hasBankAccount
        );
        const laborLetterFileValid = validaFile(
            laborLetterFile,
            "laborLetterFile",
            showAllErrors || touchedFields.laborLetterFile
        );
        const electricBillFileValid = validaFile(
            electricBillFile,
            "electricBillFile",
            showAllErrors || touchedFields.electricBillFile
        );

        return (
            companyNameValid &&
            companyEntryDateValid &&
            companyPhoneValid &&
            departmentValid &&
            municipalityValid &&
            monthlySalaryValid &&
            monthlyExpensesValid &&
            otherIncomeValid &&
            remittanceIncomeValid &&
            bankAccountValid &&
            laborLetterFileValid &&
            electricBillFileValid
        );
    };

    const handleNext = async (
        e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<HTMLFormElement>
    ) => {
        e.preventDefault();

        // Al intentar enviar, validamos y mostramos TODOS los errores
        const isValid = validateForm(true);

        if (!isValid) {
            const firstErrorElement = document.querySelector('.border-red-500');
            if (firstErrorElement) {
                firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }

            const errorCount = Object.values(validationErrors).filter(Boolean).length;

            toast.error('Información incompleta', {
                description: `Por favor complete ${errorCount} ${errorCount === 1 ? 'campo requerido' : 'campos requeridos'} correctamente.`,
            });

            return;
        }

        if (!hasChanges) {
            onNext();
            return;
        }

        setIsSubmitting(true);
        setSubmitError(null);

        try {
            await onSubmit();
            setOriginalValues({
                companyName,
                companyEntryDate,
                companyPhone,
                companyDepartmentId,
                companyMunicipalityId,
                monthlySalary,
                monthlyExpenses,
                otherIncome,
                remittanceIncome,
                hasBankAccount,
                laborLetterFile,
                electricBillFile
            });

            setHasChanges(false);
            setIsSubmitSuccess(true);
        } catch (error: any) {
            const message = error?.message || 'Error inesperado. Por favor, intente nuevamente.';
            setSubmitError(message);

            toast.error('Error al enviar', {
                description: message,
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handlePrevious = () => {
        if (onPrevious) {
            scrollToTop();
            onPrevious();
        }
    };
    return (
        <div className="space-y-8">
            <h2 className="text-xl font-semibold text-blue-700 pb-2 border-b">Datos Laborales</h2>

            {/* Mensaje de éxito con shadcn/ui Alert */}
            {isSubmitSuccess && (
                <Alert variant="default" className="bg-green-50 border-green-200 text-green-700">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <AlertTitle>Éxito</AlertTitle>
                    <AlertDescription>
                        ¡Solicitud enviada con éxito! Avanzando al siguiente paso...
                    </AlertDescription>
                </Alert>
            )}

            {/* Mensaje de error con shadcn/ui Alert */}
            {submitError && (
                <Alert variant="destructive">
                    <AlertCircle className="h-5 w-5" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>
                        {submitError}
                    </AlertDescription>
                </Alert>
            )}

            {/* 3. Sección de Verificación de Identidad */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">1</span>
                    Nombre de la empresa
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <input
                        id="companyName"
                        type="text"
                        placeholder="Nombre de la empresa"
                        value={companyName}
                        onChange={handleCompanyNameChange}
                        onBlur={() => {
                            // Al perder el foco, marcamos el campo como tocado y validamos
                            setTouchedFields(prev => ({ ...prev, companyName: true }));
                            validaCompanyName(companyName, true);
                        }}
                        className={`w-full p-3 border ${validationErrors.companyName ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    />
                    {validationErrors.companyName && (
                        <p className="text-red-500 text-sm">{validationErrors.companyName}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            {/* 3. Sección de Verificación de Identidad */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">2</span>
                    Fecha de ingreso a la empresa
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <input
                        id='companyEntryDate'
                        type="date"
                        placeholder="Fecha de ingreso a la empresa"
                        value={companyEntryDate}
                        onChange={handleCompanyEntryDateChange}
                        onBlur={() => {
                            // Al perder el foco, marcamos el campo como tocado y validamos
                            setTouchedFields(prev => ({ ...prev, companyEntryDate: true }));
                            validaCompanyEntryDate(companyEntryDate, true);
                        }}
                        className={`w-full p-3 border ${validationErrors.companyEntryDate ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    />
                    {validationErrors.companyEntryDate && (
                        <p className="text-red-500 text-sm">{validationErrors.companyEntryDate}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            {/* 3. Sección de Verificación de Identidad */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">3</span>
                    Teléfono de la empresa
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <input
                        id='telempresa'
                        type="tel"
                        placeholder="Teléfono de la empresa"
                        value={companyPhone}
                        onChange={handleCompanyPhoneChange}
                        onBlur={() => {
                            // Al perder el foco, marcamos el campo como tocado y validamos
                            setTouchedFields(prev => ({ ...prev, companyPhone: true }));
                            validaCompanyPhone(companyPhone, true);
                        }}
                        className={`w-full p-3 border ${validationErrors.companyPhone ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    />
                    {validationErrors.companyPhone && (
                        <p className="text-red-500 text-sm">{validationErrors.companyPhone}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">4</span>
                    Departamento
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <select
                        value={companyDepartmentId !== null ? companyDepartmentId.toString() : ""}
                        onChange={(e) => {
                            handleFieldChange2("companyDepartmentId", Number(e.target.value));
                            setTouchedFields(prev => ({ ...prev, companyDepartmentId: true }));
                            validaRadioField(Number(e.target.value), "companyDepartmentId", "un departamento", true);
                        }}
                        className={`w-full p-3 border ${validationErrors.companyDepartmentId ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    >
                        <option value="">Selecciona un departamento</option>
                        {departments.map((d) => (
                            <option key={d.id} value={d.id}>{d.name}</option>
                        ))}
                    </select>
                    {validationErrors.companyDepartmentId && (
                        <p className="text-red-500 text-sm">{validationErrors.companyDepartmentId}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">5</span>
                    Municipio
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <select
                        value={companyMunicipalityId !== null ? companyMunicipalityId.toString() : ""}
                        onChange={(e) => {
                            handleFieldChange2("companyMunicipalityId", Number(e.target.value));
                            setTouchedFields(prev => ({ ...prev, companyMunicipalityId: true }));
                            validaRadioField(Number(e.target.value), "companyMunicipalityId", "un municipio", true);
                        }}
                        className={`w-full p-3 border ${validationErrors.companyMunicipalityId ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    >
                        <option value="">Selecciona un municipio</option>
                        {municipalities.map((m) => (
                            <option key={m.id} value={m.id}>{m.name}</option>
                        ))}
                    </select>
                    {validationErrors.companyMunicipalityId && (
                        <p className="text-red-500 text-sm">{validationErrors.companyMunicipalityId}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            {/* 1. Sección de Monto del Crédito */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">6</span>
                    Salario mensual
                </h3>
                <p className="text-gray-600 text-sm ml-8">Selecciona el monto de tu salario mensual</p>
                <div className="text-center mb-4 mt-6">
                    {isEditingAmount ? (
                        <div className="relative inline-block">
                            <input
                                type="number"
                                value={monthlySalaryValue}
                                onChange={handleMonthlySalaryChange}
                                onBlur={handleMonthlySalaryBlur}
                                onKeyDown={handleMonthlySalaryKeyDown}
                                min={CREDIT_AMOUNT.MIN}
                                max={CREDIT_AMOUNT.MAX} autoFocus
                                className="text-center text-xl font-bold w-40 p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    ) : (
                        <span
                            className="text-3xl font-bold text-blue-600 cursor-pointer hover:underline"
                            onClick={handleMonthlySalaryClick}
                        >
                            {monthlySalary === 0 ? "Seleccione un monto" : formatCurrency(monthlySalary)}
                        </span>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Toca el monto para editarlo directamente</p>
                </div>
                <div className="space-y-4 px-4">
                    <div className="flex justify-between text-sm text-gray-500">
                        <span>{formatCurrency(CREDIT_AMOUNT.MIN)}</span>
                        <span>{formatCurrency(CREDIT_AMOUNT.MAX)}</span>
                    </div>
                    <Slider
                        value={[monthlySalary]}
                        min={CREDIT_AMOUNT.MIN}
                        max={CREDIT_AMOUNT.MAX}
                        step={CREDIT_AMOUNT.STEP}
                        onValueChange={(value) => {
                            setField("monthlySalary", value[0]);
                            setMonthlySalaryValue(value[0].toString());
                            setTouchedFields(prev => ({ ...prev, monthlySalary: true }));
                            validaPositiveAmount(value[0], "monthlySalary", true, true);
                        }}
                        className="py-4"
                    />
                    {validationErrors.monthlySalary && (
                        <p className="text-red-500 text-sm">{validationErrors.monthlySalary}</p>
                    )}
                </div>
            </div>

            {/* 1. Sección de Monto del Crédito */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">7</span>
                    Gastos mensuales
                </h3>
                <p className="text-gray-600 text-sm ml-8">Selecciona el monto de tus gastos mensuales</p>
                <div className="text-center mb-4 mt-6">
                    {isEditingMonthlyExpenses ? (
                        <div className="relative inline-block">
                            <input
                                type="number"
                                value={monthlyExpensesValue}
                                onChange={(e) => handleFieldChange(e, setMonthlyExpensesValue)}
                                onBlur={() =>
                                    handleFieldBlur(
                                        monthlyExpensesValue,
                                        "monthlyExpenses",
                                        setMonthlyExpensesValue,
                                        setIsEditingMonthlyExpenses
                                    )
                                }
                                onKeyDown={(e) => handleFieldKeyDown(e, () =>
                                    handleFieldBlur(monthlyExpensesValue, "monthlyExpenses", setMonthlyExpensesValue, setIsEditingMonthlyExpenses)
                                )}
                                min={CREDIT_AMOUNT.MIN}
                                max={CREDIT_AMOUNT.MAX} autoFocus
                                className="text-center text-xl font-bold w-40 p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    ) : (
                        <span
                            className="text-3xl font-bold text-blue-600 cursor-pointer hover:underline"
                            onClick={() => handleFieldClick(setIsEditingMonthlyExpenses, setMonthlyExpensesValue, monthlyExpenses)}
                        >
                            {monthlyExpenses === 0 ? "Seleccione un monto" : formatCurrency(monthlyExpenses)}
                        </span>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Toca el monto para editarlo directamente</p>
                </div>
                <div className="space-y-4 px-4">
                    <div className="flex justify-between text-sm text-gray-500">
                        <span>{formatCurrency(CREDIT_AMOUNT.MIN)}</span>
                        <span>{formatCurrency(CREDIT_AMOUNT.MAX)}</span>
                    </div>
                    <Slider
                        value={[monthlyExpenses]}
                        min={CREDIT_AMOUNT.MIN}
                        max={CREDIT_AMOUNT.MAX}
                        step={CREDIT_AMOUNT.STEP}
                        onValueChange={(value) => {
                            setField("monthlyExpenses", value[0]);
                            setMonthlyExpensesValue(value[0].toString());
                            setTouchedFields(prev => ({ ...prev, monthlyExpenses: true }));
                            validaPositiveAmount(value[0], "monthlyExpenses", true, true);
                        }}
                        className="py-4"
                    />
                    {validationErrors.monthlyExpenses && (
                        <p className="text-red-500 text-sm">{validationErrors.monthlyExpenses}</p>
                    )}
                </div>
            </div>

            {/* 1. Sección de Monto del Crédito */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">8</span>
                    Otros ingresos
                </h3>
                <p className="text-gray-600 text-sm ml-8">Selecciona el monto de otros ingresos</p>
                <div className="text-center mb-4 mt-6">
                    {isEditingAmount ? (
                        <div className="relative inline-block">
                            <input
                                type="number"
                                value={otherIncomeValue}
                                onChange={(e) => handleFieldChange(e, setOtherIncomeValue)}
                                onBlur={() =>
                                    handleFieldBlur(
                                        otherIncomeValue,
                                        "otherIncome",
                                        setOtherIncomeValue,
                                        setIsEditingOtherIncome
                                    )
                                }
                                onKeyDown={(e) => handleFieldKeyDown(e, () =>
                                    handleFieldBlur(otherIncomeValue, "otherIncome", setOtherIncomeValue, setIsEditingOtherIncome)
                                )}
                                min={CREDIT_AMOUNT.MIN}
                                max={CREDIT_AMOUNT.MAX} autoFocus
                                className="text-center text-xl font-bold w-40 p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    ) : (
                        <span
                            className="text-3xl font-bold text-blue-600 cursor-pointer hover:underline"
                            onClick={() => handleFieldClick(setIsEditingOtherIncome, setOtherIncomeValue, otherIncome)}                        >
                            {otherIncome === 0 ? "Seleccione un monto" : formatCurrency(otherIncome)}
                        </span>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Toca el monto para editarlo directamente</p>
                </div>
                <div className="space-y-4 px-4">
                    <div className="flex justify-between text-sm text-gray-500">
                        <span>{formatCurrency(CREDIT_AMOUNT.MIN)}</span>
                        <span>{formatCurrency(CREDIT_AMOUNT.MAX)}</span>
                    </div>
                    <Slider
                        value={[otherIncome]}
                        min={0}
                        max={30000}
                        step={100}
                        onValueChange={(value) => {
                            setField("otherIncome", value[0]);
                            setOtherIncomeValue(value[0].toString());
                            setTouchedFields(prev => ({ ...prev, otherIncome: true }));
                            validaPositiveAmount(value[0], "otherIncome", false, true);
                        }}
                        className="py-4"
                    />
                    {validationErrors.otherIncome && (
                        <p className="text-red-500 text-sm">{validationErrors.otherIncome}</p>
                    )}
                </div>
            </div>


            {/* 1. Sección de Monto del Crédito */}
            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">9</span>
                    Ingresos por remesas
                </h3>
                <p className="text-gray-600 text-sm ml-8">Selecciona el monto de ingresos por remesas</p>
                <div className="text-center mb-4 mt-6">
                    {isEditingAmount ? (
                        <div className="relative inline-block">
                            <input
                                type="number"
                                value={remittanceIncomeValue}
                                onChange={(e) => handleFieldChange(e, setRemittanceIncomeValue)}
                                onBlur={() =>
                                    handleFieldBlur(
                                        remittanceIncomeValue,
                                        "remittanceIncome",
                                        setRemittanceIncomeValue,
                                        setIsEditingRemittanceIncome
                                    )
                                }
                                onKeyDown={(e) => handleFieldKeyDown(e, () =>
                                    handleFieldBlur(remittanceIncomeValue, "remittanceIncome", setRemittanceIncomeValue, setIsEditingRemittanceIncome)
                                )}
                                min={CREDIT_AMOUNT.MIN}
                                max={CREDIT_AMOUNT.MAX} autoFocus
                                className="text-center text-xl font-bold w-40 p-2 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                    ) : (
                        <span
                            className="text-3xl font-bold text-blue-600 cursor-pointer hover:underline"
                            onClick={() => handleFieldClick(setIsEditingRemittanceIncome, setRemittanceIncomeValue, remittanceIncome)}                      >
                            {remittanceIncome === 0 ? "Seleccione un monto" : formatCurrency(remittanceIncome)}
                        </span>
                    )}
                    <p className="text-xs text-gray-500 mt-1">Toca el monto para editarlo directamente</p>
                </div>
                <div className="space-y-4 px-4">
                    <div className="flex justify-between text-sm text-gray-500">
                        <span>{formatCurrency(CREDIT_AMOUNT.MIN)}</span>
                        <span>{formatCurrency(CREDIT_AMOUNT.MAX)}</span>
                    </div>
                    <Slider
                        value={[remittanceIncome]}
                        min={0}
                        max={30000}
                        step={100}
                        onValueChange={(value) => {
                            setField("remittanceIncome", value[0]);
                            setRemittanceIncomeValue(value[0].toString());
                            setTouchedFields(prev => ({ ...prev, remittanceIncome: true }));
                            validaPositiveAmount(value[0], "remittanceIncome", false, true);
                        }}
                        className="py-4"
                    />
                    {validationErrors.remittanceIncome && (
                        <p className="text-red-500 text-sm">{validationErrors.remittanceIncome}</p>
                    )}
                </div>
            </div>


            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">10</span>
                    ¿Tienes cuenta bancaria?
                </h3>
                <div className="mt-4 px-4 space-y-2">

                    <RadioGroup
                        value={hasBankAccount?.toString() || ''}
                        onValueChange={(val) => handleRadioChange("hasBankAccount", val, "unacuenta bancaria")}
                        className="flex gap-2"
                    >
                        <div className="flex items-center space-x-2 flex-1">
                            <RadioGroupItem
                                value="1"
                                id="Si"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Si"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                  text-center flex items-center justify-center">
                                Si
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1">
                            <RadioGroupItem
                                value="2"
                                id="No"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="No"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                  text-center flex items-center justify-center">
                                No
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.hasBankAccount && (
                        <p className="text-red-500 text-sm">{validationErrors.hasBankAccount}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">11</span>
                    Carta laboral
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <input
                        type="file"
                        accept="image/*,.pdf"
                        id="laborLetterFile"
                        onChange={(e) => {
                            const file = e.target.files?.[0] ?? null;
                            setField("laborLetterFile", file);
                            setTouchedFields(prev => ({ ...prev, laborLetterFile: true }));
                            if (file) {
                                console.log("Labor letter file selected:", file.name);
                                validaFile(file, "laborLetterFile", true);
                            } else {
                                console.log("No labor letter file selected");
                                setValidationErrors(prev => ({ ...prev, laborLetterFile: "Debe adjuntar un archivo." }));
                            }
                        }}
                        className="block w-full text-sm text-gray-700 file:mr-4 file:py-2 file:px-4
                 file:rounded-md file:border-0 file:text-sm file:font-semibold
                 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />

                    {laborLetterFile && (
                        <div className="mt-2 text-sm text-gray-600">
                            Archivo seleccionado: <strong>{typeof laborLetterFile === 'string' ? laborLetterFile : laborLetterFile.name}</strong>
                        </div>
                    )}

                    {validationErrors.laborLetterFile && (
                        <p className="text-red-500 text-sm">{validationErrors.laborLetterFile}</p>
                    )}

                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">12</span>
                    Fotografia del recibo de luz
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <input
                        type="file"
                        accept="image/*,.pdf"
                        id="electricBillFile"
                        onChange={(e) => {
                            const file = e.target.files?.[0] ?? null;
                            setField("electricBillFile", file);
                            setTouchedFields(prev => ({ ...prev, electricBillFile: true }));
                            if (file) {
                                console.log("Electric bill file selected:", file.name);
                                validaFile(file, "electricBillFile", true);
                            } else {
                                console.log("No electric bill file selected");
                                setValidationErrors(prev => ({ ...prev, electricBillFile: "Debe adjuntar un archivo." }));
                            }
                        }}
                        className="block w-full text-sm text-gray-700 file:mr-4 file:py-2 file:px-4
                 file:rounded-md file:border-0 file:text-sm file:font-semibold
                 file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    />

                    {electricBillFile && (
                        <div className="mt-2 text-sm text-gray-600">
                            Archivo seleccionado: <strong>{typeof electricBillFile === 'string' ? electricBillFile : electricBillFile.name}</strong>
                        </div>
                    )}

                    {validationErrors.electricBillFile && (
                        <p className="text-red-500 text-sm">{validationErrors.electricBillFile}</p>
                    )}

                </div>
            </div>


            {/* Botones de Navegación */}
            <div className="flex justify-between pt-6 mt-8 sticky bottom-0 bg-gradient-to-t from-white py-4">
                {onPrevious ? (
                    <Button
                        onClick={handlePrevious}
                        variant="outline"
                        size="lg"
                        className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
                        disabled={isSubmitting}
                    >
                        Anterior
                    </Button>
                ) : (
                    <div className="w-[140px]"></div> // Espacio reservado para alineación
                )}

                <Button
                    onClick={handleNext}
                    className={`w-full max-w-[140px] ${isSubmitting ? 'bg-blue-400' : isFormValid ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-300'
                        }`}
                    size="lg"
                    disabled={!isFormValid || isSubmitting}
                >
                    {isSubmitting ? (
                        <div className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Enviando
                        </div>
                    ) : (
                        "Siguiente"
                    )}
                </Button>
            </div>
        </div>
    );
}