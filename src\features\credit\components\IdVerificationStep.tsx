import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { IdVerificationStepProps } from "../types";

/**
 * Step for verifying the user's national ID
 */
export function IdVerificationStep({
  nationalId,
  setNationalId,
  onNext,
  onPrevious
}: IdVerificationStepProps) {
  const [error, setError] = useState("");

  const validateAndContinue = () => {
    // Simple validation - ID should be at least 8 characters
    if (nationalId.length < 8) {
      setError("El número de identificación debe tener al menos 8 caracteres");
      return;
    }

    onNext();
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-800">Identificación Nacional</h2>
      <p className="text-gray-600">Ingresa tu número de identificación nacional</p>

      <div className="space-y-2 mt-6">
        <Label htmlFor="nationalId">Número de ID</Label>
        <Input
          id="nationalId"
          value={nationalId}
          onChange={(e) => {
            setNationalId(e.target.value);
            if (error) setError("");
          }}
          className={error ? "border-red-500" : ""}
          placeholder="Ejemplo: 1234567890"
        />
        {error && <p className="text-sm text-red-500 mt-1">{error}</p>}
      </div>

      <div className="grid grid-cols-2 gap-3 mt-8">
        <Button
          onClick={onPrevious}
          variant="outline"
          className="w-full"
          size="lg"
        >
          Anterior
        </Button>
        <Button
          onClick={validateAndContinue}
          className="w-full"
          size="lg"
        >
          Siguiente
        </Button>
      </div>
    </div>
  );
}
