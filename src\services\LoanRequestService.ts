import axios, { AxiosError, Axios<PERSON>nst<PERSON>, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Generic API response interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  details?: string;
}

/**
 * Step1 request interface (Credit Amount & Basic Info)
 */
export interface Step1Request {
  requestedAmount: number;
  loanTerm: number;
  identificationNumber: string;
  withdrawalMethodId: number;
  branchId: string;
  location: string;
  latitude: number;
  longitude: number;

}

/**
 * Step2 request interface (Client Personal Data)
 */
interface Step2Request {
  loanRequestId: string;
  name1: string;
  name2: string;
  name3: string;
  lastName1: string;
  lastName2: string;
  birth: string;
  phone: string;
  phone2: string;
  sexId: number;
  marital: number;
  education: number;
  income: number;
  zipCode: string;
}

interface ResidenceStepRequest {
  loanRequestId: string;
  residenceType: number;
  departmentId: number;
  municipalityId: number;
  address: string;
  livingTime: number;
  isPep: number;
  isCpe: number;
  dependents: number;
  vehicleType: number;
}

export interface WorkStepRequest {
  loanRequestId: string;
  companyName: string;
  companyEntryDate: string; // formato ISO yyyy-mm-dd
  companyPhone: string;
  companyDepartmentId: number;
  companyMunicipalityId: number;
  monthlySalary: number;
  monthlyExpenses: number;
  otherIncome: number;
  remittanceIncome: number;
  hasBankAccount: number; // 1 o 2
}

export interface ReferenceData {
  fullName: string;
  phone: string;
  address: string;
  relationship: string;
}

export interface ReferenceStepRequest {
  loanRequestId: number;
  references: [ReferenceData, ReferenceData];
}

/**
 * Step3 request interface (Credit Conditions)
 */
export interface Step3Request {
  loanRequestId: string;
  agreedToTerms?: boolean;
}

/**
 * Step4 request interface (ID Verification)
 */
export interface Step4Request {
  loanRequestId: string;
  // Add fields specific to ID verification
}

/**
 * Step5 request interface (Photographs/ID Images)
 */
export interface Step5Request {
  loanRequestId: string;
  // Add additional metadata if needed
}

/**
 * Step6 request interface (Final Submission)
 */
export interface Step6Request {
  loanRequestId: string;
  // Add any final submission fields
}

/**
 * Generic response interface for all loan request steps
 */
export interface LoanRequestResponse {
  clientId?: number;
  success: boolean;
  message?: string;
  errors?: Record<string, string[]>;
}

/**
 * Comprehensive service for handling loan requests with built-in API functionality
 */
class LoanRequestService {
  private api: AxiosInstance;
  private readonly baseUrl = 'api/v1/loan-requests';

  constructor() {
    // Initialize Axios instance with base URL from environment variables
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000 // 30 seconds timeout
    });

    // Set up request and response interceptors
    this.setupInterceptors();
  }

  /**
   * Configure Axios interceptors for request and response handling
   */
  private setupInterceptors(): void {
    // Request interceptor - runs before each request
    this.api.interceptors.request.use(
      (config) => {
        // Get auth token if it exists
        const token = localStorage.getItem('auth_token');

        // Add token to Authorization header if available
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, config.data || '');
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(this.formatError(error));
      }
    );

    // Response interceptor - runs after each response
    this.api.interceptors.response.use(
      (response) => {
        console.log(`API Response [${response.status}]:`, response.data);
        return response;
      },
      (error) => {
        console.error('Response error:', error);
        return Promise.reject(this.formatError(error));
      }
    );
  }

  /**
   * Format error responses consistently
   */
  private formatError(error: AxiosError): any {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      const status = error.response.status;

      // Handle 401 Unauthorized
      if (status === 401) {
        localStorage.removeItem('auth_token');
        // You can redirect to login page here if needed
        // window.location.href = '/login';
      }

      return {
        success: false,
        status,
        error: error.message,
        details: error.response.data
      };
    } else if (error.request) {
      // The request was made but no response was received
      return {
        success: false,
        error: 'No response received from server',
        details: 'Please check your network connection'
      };
    } else {
      // Something happened in setting up the request
      return {
        success: false,
        error: error.message || 'Unknown error occurred',
        details: 'Error setting up the request'
      };
    }
  }

  /**
   * Generic GET request method
   */
  private async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.get<T>(url, config);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error(`GET ${url} error:`, error);
      return error;
    }
  }

  /**
   * Generic POST request method
   */
  private async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.api.post<T>(url, data, config);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error(`POST ${url} error:`, error);
      return error;
    }
  }

  /**
   * Generic file upload method using FormData
   */
  private async upload<T = any>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const uploadConfig: AxiosRequestConfig = {
        ...config,
        headers: {
          ...config?.headers,
          'Content-Type': 'multipart/form-data'
        }
      };

      const response = await this.api.post<T>(url, formData, uploadConfig);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error(`UPLOAD ${url} error:`, error);
      return error;
    }
  }

  /**
   * Submit Step 1: Credit Amount and Basic Info
   * @param data Request data for Step 1
   * @returns API response
   */

  async submitStep1(data: Step1Request): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step 1 with data:', data);

      // Convert frontend camelCase to backend PascalCase for .NET
      const requestBody = {
        RequestedAmount: data.requestedAmount,
        LoanTerm: data.loanTerm,
        IdentificationNumber: data.identificationNumber,
        WithdrawalMethodId: data.withdrawalMethodId,
        branchId: data.branchId,
        location: data.location,
        latitude: data.latitude,
        longitude: data.longitude
      };

      console.log('Request Body:', requestBody);

      // Configurar los headers con el token JWT
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.n4HYOs3if-AEJrwn-ywGi45xqOXcC4CYA42veYPtAASFH2wYPjGlUvm8VA165XFruZOWvRR-8WZhYKV0OLGBsQ`
      };

      return await this.post<LoanRequestResponse>(`${this.baseUrl}/step1`, requestBody, { headers });

    } catch (error: any) {
      console.error('Error submitting Step 1:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step 1',
        details: error.details || 'Unknown error occurred'
      };
    }
  }

  /**
   * Submit Step 2: Client Personal Data
   * @param data Request data for Step 2
   * @returns API response
   */
  async submitStep2(data: Step2Request): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step 2 with data:', data);

      // Convert frontend camelCase to backend PascalCase for .NET
      const requestBody = {
        clientId: Number(data.loanRequestId),
        FirstName: data.name1,
        secondName: data.name2,
        thirdName: data.name3,
        firstLastName: data.lastName1,

        secondLastName: data.lastName2,
        birthDate: data.birth,
        phoneNumber: data.phone,
        additionalPhoneNumber: data.phone2,
        sexId: data.sexId,

        maritalStatusId: data.marital,
        scholarityLevelId: data.education,
        incomeSourceTypeId: data.income,
        zipCode: data.zipCode
      };

      console.log('Request2 Body: ', requestBody);

      // Configurar los headers con el token JWT
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.n4HYOs3if-AEJrwn-ywGi45xqOXcC4CYA42veYPtAASFH2wYPjGlUvm8VA165XFruZOWvRR-8WZhYKV0OLGBsQ`
      };

      return await this.post<LoanRequestResponse>(`${this.baseUrl}/step2`, requestBody, { headers });
    } catch (error: any) {
      console.error('Error submitting Step 2:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step 2',
        details: error.details || 'Unknown error occurred'
      };
    }
  }

  async submitStepResidence(data: ResidenceStepRequest): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step Residence with data:', data);

      if (data.livingTime === 7) data.livingTime = 6;

      const requestBody = {
        clientId: Number(data.loanRequestId),
        residenceOwnershipTypeId: data.residenceType,
        departmentId: data.departmentId,
        municipalityId: data.municipalityId,
        address: data.address,
        timeInResidenceTypeId: data.livingTime,
        isPep: data.isPep === 1,
        isCpe: data.isCpe === 1,
        dependentsQuantityId: data.dependents,
        vehicleTypeId: data.vehicleType
      };

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.n4HYOs3if-AEJrwn-ywGi45xqOXcC4CYA42veYPtAASFH2wYPjGlUvm8VA165XFruZOWvRR-8WZhYKV0OLGBsQ`
      };

      const response = await this.post<LoanRequestResponse>(`${this.baseUrl}/step4`, requestBody, { headers });

      console.log('Step Residence submitted successfully');
      return response;
    } catch (error: any) {
      console.error('Error submitting Step Residence:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step Residence',
        details: error.details || 'Unknown error occurred'
      };
    }
  }


  async submitStepLaboral(data: {
    loanRequestId: string;
    companyName: string;
    companyEntryDate: string;
    companyPhone: string;
    companyDepartmentId: number;
    companyMunicipalityId: number;
    monthlySalary: number;
    monthlyExpenses: number;
    otherIncome: number;
    remittanceIncome: number;
    hasBankAccount: string; // "true" | "false"
    attachments: Array<{ file: File; attachmentTypeId: number }>;
  }): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('📨 Enviando Step Laboral con FormData:', data);

      const formData = new FormData();

      formData.append("clientId", data.loanRequestId);
      formData.append("companyName", data.companyName);
      formData.append("hireDate", data.companyEntryDate);
      formData.append("companyPhoneNumber", data.companyPhone);
      formData.append("departmentId", String(data.companyDepartmentId));
      formData.append("municipalityId", String(data.companyMunicipalityId));
      formData.append("monthlyIncome", String(data.monthlySalary));
      formData.append("monthlyExpenses", String(data.monthlyExpenses));
      formData.append("remittanceIncome", String(data.remittanceIncome));
      formData.append("additionalIncome", String(data.otherIncome));
      formData.append("hasBankAccount", data.hasBankAccount);

      // Archivos con índice
      data.attachments.forEach((attachment, index) => {
        formData.append(`attachments[${index}].file`, attachment.file);
        formData.append(`attachments[${index}].attachmentTypeId`, String(attachment.attachmentTypeId));
      });

      const headers = {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.n4HYOs3if-AEJrwn-ywGi45xqOXcC4CYA42veYPtAASFH2wYPjGlUvm8VA165XFruZOWvRR-8WZhYKV0OLGBsQ`
        // NO se define Content-Type. El navegador lo pone automáticamente.
      };

      console.log('🔗 URL final:', `${this.baseUrl}/step5`);

      const response = await this.api.post<LoanRequestResponse>(
        `${this.baseUrl}/step5`,
        formData,
        { headers }
      );

      console.log('✅ Step Laboral enviado correctamente');
      return {
        success: true,
        data: response.data,
      };

    } catch (error: any) {
      console.error('❌ Error enviando Step Laboral:', error);
      return {
        success: false,
        error: error.message || 'Fallo en envío de Step Laboral',
        details: error.details || 'Error desconocido',
      };
    }
  }

  async submitStepNegocio(data: {
    loanRequestId: string;
    companyName: string;
    companyEntryDate: string;
    companyPhone: string;
    companyDepartmentId: number;
    companyMunicipalityId: number;
    monthlySalary: number;
    monthlyExpenses: number;
    otherIncome: number;
    remittanceIncome: number;
    hasBankAccount: string; // "true" | "false"
    nit: number;
    economicActivityId: number;
    businessOwnershipTypeId: number;
    address: string;
    attachments: Array<{ file: File; attachmentTypeId: number }>;
  }): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('📨 Enviando Step Laboral con FormData:', data);

      const formData = new FormData();

      formData.append("clientId", data.loanRequestId);
      formData.append("companyName", data.companyName);
      formData.append("hireDate", data.companyEntryDate);
      formData.append("companyPhoneNumber", data.companyPhone);
      formData.append("departmentId", String(data.companyDepartmentId));
      formData.append("municipalityId", String(data.companyMunicipalityId));
      formData.append("monthlyIncome", String(data.monthlySalary));
      formData.append("monthlyExpenses", String(data.monthlyExpenses));
      formData.append("remittanceIncome", String(data.remittanceIncome));
      formData.append("additionalIncome", String(data.otherIncome));
      formData.append("hasBankAccount", data.hasBankAccount);
      formData.append("nit", String(data.nit));
      formData.append("economicActivityId", String(data.economicActivityId));
      formData.append("businessOwnershipTypeId", String(data.businessOwnershipTypeId));
      formData.append("address", data.address);

      // Archivos con índice
      data.attachments.forEach((attachment, index) => {
        formData.append(`attachments[${index}].file`, attachment.file);
        formData.append(`attachments[${index}].attachmentTypeId`, String(attachment.attachmentTypeId));
      });

      const headers = {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.n4HYOs3if-AEJrwn-ywGi45xqOXcC4CYA42veYPtAASFH2wYPjGlUvm8VA165XFruZOWvRR-8WZhYKV0OLGBsQ`
        // NO se define Content-Type. El navegador lo pone automáticamente.
      };

      console.log('🔗 URL final:', `${this.baseUrl}/step5`);

      const response = await this.api.post<LoanRequestResponse>(
        `${this.baseUrl}/step5`,
        formData,
        { headers }
      );

      console.log('✅ Step Laboral enviado correctamente');
      return {
        success: true,
        data: response.data,
      };

    } catch (error: any) {
      console.error('❌ Error enviando Step Laboral:', error);
      return {
        success: false,
        error: error.message || 'Fallo en envío de Step Laboral',
        details: error.details || 'Error desconocido',
      };
    }
  }

  async submitStepReferencias(data: {
    clientId: number;
    referers: {
      fullName: string;
      phoneNumber: string;
      address: string;
      kihsipId: number;
    }[];
    image?: File; // ⬅️ Soporte futuro para imagen
  }): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      const formData = new FormData();

      formData.append("clientId", String(data.clientId));
      formData.append("Referers", JSON.stringify(data.referers));

      if (data.image) {
        formData.append("signature", data.image);
      }

      console.log('Formulario a enviar data :', data);

      const headers = {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer tu_token_aqui`
        // No pongas Content-Type, el navegador lo maneja con boundaries correctos
      };

      const response = await this.api.post<LoanRequestResponse>(
        `${this.baseUrl}/step6`,
        formData,
        { headers }
      );

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      console.error("❌ Error enviando referencias:", error);
      return {
        success: false,
        error: error.message || "Fallo al enviar referencias",
        details: error?.response?.data || "Error desconocido",
      };
    }
  }


  // async submitStepReferencias(data: {
  //   clientId: number;
  //   referers: {
  //     fullName: string;
  //     phoneNumber: string;
  //     address: string;
  //     kihsipId: number;
  //   }[];
  // }): Promise<ApiResponse<LoanRequestResponse>> {
  //   try {
  //     console.log("Submitting Step Referencias with data:", data);

  //     const headers = {
  //       "Content-Type": "application/json",
  //       Authorization: `Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************.n4HYOs3if-AEJrwn-ywGi45xqOXcC4CYA42veYPtAASFH2wYPjGlUvm8VA165XFruZOWvRR-8WZhYKV0OLGBsQ`
  //     };

  //     const response = await this.post<LoanRequestResponse>(`${this.baseUrl}/step6`,
  //       data,
  //       { headers }
  //     );

  //     console.log("Step Referencias submitted successfully");
  //     return {
  //       success: true,
  //       data: response.data,
  //     };
  //   } catch (error: any) {
  //     console.error("Error submitting Step Referencias:", error);
  //     return {
  //       success: false,
  //       error: error.message || "Failed to submit Step Referencias",
  //       details: error?.response?.data || "Unknown error occurred",
  //     };
  //   }
  // }



  /**
     * Submit Step 3: Credit Conditions
     * @param data Request data for Step 3
     * @returns API response
     */
  async submitStep3(data: Step3Request): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step 3 with data:', data);

      // Convert frontend camelCase to backend PascalCase for .NET
      const requestBody = {
        LoanRequestId: data.loanRequestId,
        AgreedToTerms: data.agreedToTerms
      };

      return await this.post<LoanRequestResponse>(`${this.baseUrl}/step3`, requestBody);

    } catch (error: any) {
      console.error('Error submitting Step 3:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step 3',
        details: error.details || 'Unknown error occurred'
      };
    }
  }

  /**
   * Submit Step 4: ID Verification
   * @param data Request data for Step 4
   * @returns API response
   */
  async submitStep4(data: Step4Request): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step 4 with data:', data);

      // Convert frontend camelCase to backend PascalCase for .NET
      const requestBody = {
        LoanRequestId: data.loanRequestId
        // Add other fields as needed
      };

      return await this.post<LoanRequestResponse>(`${this.baseUrl}/step4`, requestBody);
    } catch (error: any) {
      console.error('Error submitting Step 4:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step 4',
        details: error.details || 'Unknown error occurred'
      };
    }
  }

  /**
   * Submit Step 5: Photographs/ID Images
   * @param data Request data for Step 5
   * @param frontIdImage Front ID image file
   * @param backIdImage Back ID image file
   * @param selfieImage Selfie image file
   * @returns API response
   */
  async submitStep5(
    data: Step5Request,
    frontIdImage?: File,
    backIdImage?: File,
    selfieImage?: File
  ): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step 5 with data:', data);

      // Create FormData for file uploads
      const formData = new FormData();
      formData.append('LoanRequestId', data.loanRequestId);

      // Append files if provided
      if (frontIdImage) {
        formData.append('FrontIdImage', frontIdImage);
      }

      if (backIdImage) {
        formData.append('BackIdImage', backIdImage);
      }

      if (selfieImage) {
        formData.append('SelfieImage', selfieImage);
      }

      // Use upload method for handling files
      return await this.upload<LoanRequestResponse>(`${this.baseUrl}/step5`, formData);
    } catch (error: any) {
      console.error('Error submitting Step 5:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step 5',
        details: error.details || 'Unknown error occurred'
      };
    }
  }

  /**
   * Submit Step 6: Final Submission
   * @param data Request data for Step 6
   * @returns API response
   */
  async submitStep6(data: Step6Request): Promise<ApiResponse<LoanRequestResponse>> {
    try {
      console.log('Submitting Step 6 with data:', data);

      // Convert frontend camelCase to backend PascalCase for .NET
      const requestBody = {
        LoanRequestId: data.loanRequestId
        // Add other fields as needed
      };

      return await this.post<LoanRequestResponse>(`${this.baseUrl}/step6`, requestBody);
    } catch (error: any) {
      console.error('Error submitting Step 6:', error);
      return {
        success: false,
        error: error.message || 'Failed to submit Step 6',
        details: error.details || 'Unknown error occurred'
      };
    }
  }

  /**
   * Validate Step 1 input data
   * @param data Step 1 request data
   * @returns Validation result with errors if invalid
   */
  validateStep1(data: Step1Request): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Validate requested amount
    if (!data.requestedAmount || data.requestedAmount <= 0) {
      errors.requestedAmount = 'Please enter a valid amount greater than zero';
    }

    // Validate loan term
    if (!data.loanTerm || data.loanTerm <= 0) {
      errors.loanTerm = 'Please enter a valid loan term';
    }

    // Validate identification number
    if (!data.identificationNumber || data.identificationNumber.trim() === '') {
      errors.identificationNumber = 'Identification number is required';
    }

    // Validate withdrawal method
    if (!data.withdrawalMethodId) {
      errors.withdrawalMethodId = 'Please select a withdrawal method';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  /**
   * Validate Step 2 input data (Client Personal Data)
   * @param data Step 2 request data
   * @returns Validation result with errors if invalid
   */
  validateStep2(data: Step2Request): { isValid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Validate loan request ID
    // if (!data.loanRequestId || data.loanRequestId.trim() === '') {
    //   errors.loanRequestId = 'Loan request ID is required';
    // }

    // Validate first name
    if (!data.name1 || data.name1.trim() === '') {
      errors.firstName = 'Primer nombre es requerido';
    }

    // Validate last name
    if (!data.lastName1 || data.lastName1.trim() === '') {
      errors.lastName = 'Apellido requerido';
    }

    // Validate email
    if (data.phone) {

      if (!data.phone || data.phone) {
        errors.email = 'Teléfono requerido';
      }
    }

    // Validate phone number
    // if (!data.education || data.education.trim() === '') {
    //   errors.phoneNumber = 'Phone number is required';
    // }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  // Add validation methods for other steps as needed

  /**
   * Get access to the underlying Axios instance
   * @returns Axios instance
   */
  getInstance(): AxiosInstance {
    return this.api;
  }
}

// Create and export a singleton instance
export const loanRequestService = new LoanRequestService();
export default loanRequestService;