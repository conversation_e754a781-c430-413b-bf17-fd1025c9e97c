/* Fix for Leaflet marker icons in React */
.leaflet-marker-icon,
.leaflet-marker-shadow {
  /* Fix for marker icons disappearing after zoom */
  will-change: auto !important;
}

/* Make sure popups are visible and styled nicely */
.leaflet-popup-content-wrapper {
  padding: 8px;
  border-radius: 8px;
}

.leaflet-popup-content {
  margin: 8px;
  line-height: 1.4;
}

.leaflet-popup-content p {
  margin: 4px 0;
}

.leaflet-popup-content strong {
  display: block;
  margin-bottom: 4px;
  color: #3b82f6;
}

.leaflet-popup-content button {
  display: inline-block;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.leaflet-popup-content button:hover {
  background-color: #dbeafe;
}

/* Make sure the map container has a proper height */
.leaflet-container {
  height: 100%;
  width: 100%;
}
