import { useRef, useState, useCallback, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Webcam } from "@webcam/react";
import VideoPreviewModal from "@/components/VideoPreviewModal";
import { authService } from '../../../../services/AuthenticationService';

interface SelfieComponentProps {
  selfiePhoto: File | null;
  setSelfiePhoto: (file: File | null) => void;
  onSubmit: () => void;
  onPrevious: () => void;
  processId?: string | null;
  validateSelfie?: (file: File) => Promise<boolean>;
  error?: string | null;
  setError?: (error: string | null) => void;
}

interface VideoDevice {
  deviceId: string;
  label: string;
}


/**
 * Componente mejorado para el video selfie con selección de cámara y guía facial
 */
export function SelfieComponent({
  selfiePhoto,
  setSelfiePhoto,
  onSubmit,
  onPrevious,
  processId,
  validateSelfie,
  error,
  setError
}: SelfieComponentProps) {
  // Estados para manejo de cámara y grabación
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [countdown, setCountdown] = useState<number | null>(null);
  const [recordedSeconds, setRecordedSeconds] = useState(0);
  const [recordingComplete, setRecordingComplete] = useState(false);
  const [isFaceCentered, setIsFaceCentered] = useState(false);
  const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false);

  // States for camera devices
  const [videoDevices, setVideoDevices] = useState<VideoDevice[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>("");
  const [isCameraSelectionOpen, setIsCameraSelectionOpen] = useState(false);

  // Estados para validación
  const [isValidating, setIsValidating] = useState(false);
  const [validationSuccess, setValidationSuccess] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);

  // Modal state for preview
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewVideoUrl, setPreviewVideoUrl] = useState<string | null>(null);
  const [previewFilename, setPreviewFilename] = useState("selfie_video.mp4");

  // Referencias para manejo de cámara y grabación
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const getSnapshotRef = useRef<null | ((opts?: { quality?: number }) => string | undefined)>(null);
  const videoElementRef = useRef<HTMLVideoElement | null>(null);
  const faceCheckIntervalRef = useRef<number | null>(null);
  const videoUrlRef = useRef<string | null>(null);
  const videoFormatRef = useRef<string>("webm"); // To track what format the MediaRecorder is actually using
  const stableCenteringRef = useRef<{ lastCentered: boolean; counter: number }>({
    lastCentered: false,
    counter: 0
  });

  const openLastVideoPreview = () => {
    const lastVideo = authService.getLastSelfieVideo();
    if (lastVideo) {
      setPreviewVideoUrl(lastVideo.url);
      setPreviewFilename(lastVideo.filename);
      setShowPreviewModal(true);
    } else {
      console.error('No video available to preview');
      if (setError) setError('No hay un video disponible para previsualizar');
    }
  };

  // Get list of available cameras
  useEffect(() => {
    async function getVideoDevices() {
      try {
        // First request permission to access media devices
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });

        // Stop the stream immediately (we're just requesting permission)
        stream.getTracks().forEach(track => track.stop());

        // Once permission is granted, enumerate devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoInputs = devices.filter(device => device.kind === 'videoinput');

        setCameraPermissionGranted(true);

        const formattedDevices = videoInputs.map((device, index) => ({
          deviceId: device.deviceId,
          label: device.label || `Cámara ${index + 1}`
        }));

        setVideoDevices(formattedDevices);

        // Select front camera by default if available
        const frontCamera = formattedDevices.find(device =>
          device.label.toLowerCase().includes('front') ||
          device.label.toLowerCase().includes('frontal') ||
          device.label.toLowerCase().includes('selfie')
        );

        if (frontCamera) {
          setSelectedDeviceId(frontCamera.deviceId);
        } else if (formattedDevices.length > 0) {
          setSelectedDeviceId(formattedDevices[0].deviceId);
        }
      } catch (err) {
        console.error("Error accessing camera:", err);
        if (setError) setError("No se pudo acceder a la cámara. Por favor, verifique los permisos.");
      }
    }

    getVideoDevices();

    // Add listener for device changes
    navigator.mediaDevices.addEventListener('devicechange', getVideoDevices);

    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getVideoDevices);
    };
  }, [setError]);

  // Clean up video URL when component unmounts
  useEffect(() => {
    return () => {
      if (videoUrlRef.current) {
        URL.revokeObjectURL(videoUrlRef.current);
        videoUrlRef.current = null;
      }
      if (previewVideoUrl) {
        URL.revokeObjectURL(previewVideoUrl);
      }
    };
  }, [previewVideoUrl]);

  // Timer for recording
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRecording) {
      interval = setInterval(() => {
        setRecordedSeconds(prev => {
          // Stop recording after 5 seconds
          if (prev >= 5) {
            stopRecording();
            return 5;
          }
          return prev + 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording]);

  // Timer for countdown
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (countdown !== null && countdown > 0) {
      interval = setInterval(() => {
        setCountdown(prev => {
          if (prev !== null && prev > 1) {
            return prev - 1;
          } else {
            // Start recording when countdown reaches 0
            startRecording();
            return null;
          }
        });
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [countdown]);

  // Function to activate camera and get stream
  const activateCamera = useCallback(() => {
    setIsCameraSelectionOpen(true);
    if (setError) setError(null);
    setValidationSuccess(false);
    setValidationMessage(null);
  }, [setError]);

  // Function to select camera and start it
  const selectCamera = useCallback((deviceId: string) => {
    setSelectedDeviceId(deviceId);
    setIsCameraSelectionOpen(false);
    setIsCameraActive(true);

    // Stop any existing stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Get stream with selected camera
    navigator.mediaDevices.getUserMedia({
      video: {
        deviceId: { exact: deviceId },
        width: { ideal: 640 },
        height: { ideal: 480 },
        facingMode: "user"
      },
      audio: true
    })
      .then(stream => {
        streamRef.current = stream;

        // Start checking if face is centered
        startFaceCenteringCheck();
      })
      .catch(err => {
        console.error("Error accessing camera:", err);
        if (setError) setError("No se pudo acceder a la cámara seleccionada. Por favor, intente con otra.");
        setIsCameraActive(false);
      });
  }, [setError]);

  // Function to check if face is centered (improved version with stability)
  const startFaceCenteringCheck = () => {
    // Clear any existing interval
    if (faceCheckIntervalRef.current) {
      window.clearInterval(faceCheckIntervalRef.current);
    }

    // Reset face centering state
    setIsFaceCentered(false);
    stableCenteringRef.current = { lastCentered: false, counter: 0 };

    // Create a new interval to check
    faceCheckIntervalRef.current = window.setInterval(() => {
      // In a real implementation, you'd use a face detection library
      // This simulation makes the state more stable and doesn't fluctuate randomly

      if (stableCenteringRef.current.lastCentered) {
        // 90% chance to stay centered once centered
        const centered = Math.random() > 0.1;
        setIsFaceCentered(centered);
        stableCenteringRef.current.lastCentered = centered;
      } else {
        // When not centered, gradually increase chance of becoming centered
        stableCenteringRef.current.counter += 1;
        const centered = Math.random() > (0.7 - (stableCenteringRef.current.counter * 0.1));
        setIsFaceCentered(centered);

        if (centered) {
          stableCenteringRef.current.lastCentered = true;
          stableCenteringRef.current.counter = 0;
        }
      }
    }, 500);
  };

  // Clear interval when component unmounts
  useEffect(() => {
    return () => {
      if (faceCheckIntervalRef.current) {
        window.clearInterval(faceCheckIntervalRef.current);
      }
    };
  }, []);

  // Monitor face centering during recording
  useEffect(() => {
    let warningShown = false;
    let interval: NodeJS.Timeout | null = null;

    if (isRecording) {
      interval = setInterval(() => {
        if (!isFaceCentered && !warningShown) {
          console.log("Warning: Face not centered during recording");
          warningShown = true;
        } else if (isFaceCentered && warningShown) {
          console.log("Face re-centered during recording");
          warningShown = false;
        }
      }, 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, isFaceCentered]);

  // Function to start countdown
  const prepareRecording = () => {
    if (!isFaceCentered) {
      if (setError) setError("Por favor, centra tu rostro en el círculo antes de grabar.");
      return;
    }

    if (setError) setError(null);
    setCountdown(3); // Start 3-second countdown
    setRecordedSeconds(0);
    setRecordingComplete(false);
  };

  // Function to start recording
  const startRecording = useCallback(() => {
    // Verify we have access to the camera
    if (!streamRef.current && videoElementRef.current?.srcObject) {
      streamRef.current = videoElementRef.current.srcObject as MediaStream;
    }

    if (!streamRef.current) {
      console.error("No media stream available");
      if (setError) setError("No se pudo acceder a la cámara. Por favor, verifique los permisos.");
      return;
    }

    setIsRecording(true);
    setRecordedSeconds(0);
    chunksRef.current = [];

    try {
      // Use more reliable WebM format with VP8 codec
      let options: MediaRecorderOptions = {
        mimeType: 'video/webm;codecs=vp8',
        videoBitsPerSecond: 1500000 // Lower to 1.5Mbps for better reliability
      };

      // Check if the browser supports our preferred format
      if (!MediaRecorder.isTypeSupported(options.mimeType)) {
        console.log("VP8 codec not supported, trying basic webm");
        options = {
          mimeType: 'video/webm',
          videoBitsPerSecond: 1500000
        };

        if (!MediaRecorder.isTypeSupported(options.mimeType)) {
          console.log("WebM not supported, trying MP4");
          options = {
            mimeType: 'video/mp4',
            videoBitsPerSecond: 1500000
          };

          if (!MediaRecorder.isTypeSupported(options.mimeType)) {
            console.log("Falling back to browser default format");
            options = {
              mimeType: '',
              videoBitsPerSecond: 1500000
            };
          }
        }
      }

      // Track the format we're using
      videoFormatRef.current = options.mimeType.split('/')[1]?.split(';')[0] || 'webm';
      console.log(`Using video format: ${options.mimeType || "browser default"}, tracked as: ${videoFormatRef.current}`);

      const mediaRecorder = new MediaRecorder(streamRef.current, options);

      // Collect data less frequently (500ms instead of 100ms)
      const chunkInterval = 500;

      mediaRecorder.ondataavailable = (e) => {
        if (e.data && e.data.size > 0) {
          console.log(`Received video chunk: ${(e.data.size / 1024).toFixed(2)} KB`);
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorder.onstop = () => {
        // First, determine the final format based on what the MediaRecorder used
        const actualMimeType = mediaRecorder.mimeType || `video/${videoFormatRef.current}`;
        console.log(`MediaRecorder used format: ${actualMimeType}`);

        // Create a blob with the matching MIME type
        const blob = new Blob(chunksRef.current, { type: actualMimeType });
        
        // Create a file with matching extension and MIME type
        const fileExtension = videoFormatRef.current === 'mp4' ? 'mp4' : 'webm';
        const fileName = `selfie_video_${processId || 'temp'}.${fileExtension}`;
        const file = new File([blob], fileName, { type: actualMimeType });

        // Log for debugging
        console.log("Video recorded successfully:", {
          name: file.name,
          type: file.type,
          size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
          chunks: chunksRef.current.length
        });

        // Verify the file has actual content
        if (file.size < 1000) { // Less than 1KB is suspicious
          if (setError) setError("El video grabado es demasiado pequeño. Por favor, grabe nuevamente.");
          return;
        }

        // Check size
        if (file.size > 10 * 1024 * 1024) {
          if (setError) setError("El video es demasiado grande. Por favor, grabe uno más corto.");
          return;
        }

        // Clean up previous URL if it exists
        if (videoUrlRef.current) {
          URL.revokeObjectURL(videoUrlRef.current);
          videoUrlRef.current = null;
        }
        
        // Set the recording as complete BEFORE creating the video URL
        setRecordingComplete(true);
        
        // Save the file to our state
        setSelfiePhoto(file);

        // Create a URL for the video element with a slight delay to ensure the video element is rendered
        setTimeout(() => {
          try {
            if (videoRef.current) {
              const videoURL = URL.createObjectURL(blob);
              videoUrlRef.current = videoURL;
              videoRef.current.src = videoURL;
              
              // Save for modal preview
              setPreviewVideoUrl(videoURL);
              setPreviewFilename(file.name);
              
              console.log("Video URL created:", videoURL);
              
              videoRef.current.onloadedmetadata = () => {
                console.log("Video loaded with duration:", videoRef.current?.duration || "unknown");
                // Attempt to play the video
                videoRef.current?.play().catch(e => {
                  console.error("Error playing video:", e);
                });
              };
              
              videoRef.current.onerror = (e) => {
                console.error("Error loading recorded video:", e);
                if (setError) setError("Error al reproducir el video grabado. Por favor, intente nuevamente.");
              };
            } else {
              console.error("Video reference is not available");
              if (setError) setError("Error al mostrar el video grabado. Por favor, intente nuevamente.");
            }
          } catch (err) {
            console.error("Error creating video URL:", err);
            if (setError) setError("Error al procesar el video grabado. Por favor, intente nuevamente.");
          }
        }, 100); // Short delay to ensure the videoRef is properly attached

        // Validate the video if we have processId and validation function
        if (processId && validateSelfie) {
          validateSelfieWithAPI(file);
        }
      };

      mediaRecorderRef.current = mediaRecorder;
      
      // Request larger chunk sizes for more stable recording
      mediaRecorder.start(chunkInterval);
      
      console.log(`Started recording with ${chunkInterval}ms chunks`);

    } catch (err) {
      console.error("Error al iniciar la grabación:", err);
      if (setError) setError("No se pudo iniciar la grabación. Por favor, verifique los permisos de cámara.");
    }
  }, [setSelfiePhoto, processId, validateSelfie, setError]);

  // Function to stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      try {
        mediaRecorderRef.current.stop();
        console.log("Stopped recording. Chunks collected:", chunksRef.current.length);
      } catch (err) {
        console.error("Error stopping mediaRecorder:", err);
      }
      setIsRecording(false);
    }
  }, [isRecording]);

  // Function to cancel recording
  const cancelRecording = () => {
    if (isRecording) {
      stopRecording();
    }
    setIsCameraActive(false);
    setRecordingComplete(false);
    if (videoRef.current) {
      videoRef.current.src = "";
    }

    // Clean up video URL
    if (videoUrlRef.current) {
      URL.revokeObjectURL(videoUrlRef.current);
      videoUrlRef.current = null;
    }

    // Stop and release the camera
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Stop face centering check
    if (faceCheckIntervalRef.current) {
      window.clearInterval(faceCheckIntervalRef.current);
    }
  };

  // Function to validate selfie with API
  const validateSelfieWithAPI = async (file: File) => {
    if (!processId || !validateSelfie) return;

    setIsValidating(true);
    if (setError) setError(null);
    setValidationSuccess(false);
    setValidationMessage(null);

    try {
      console.log("Validating selfie video:", file.name, file.type, file.size);
      const isValid = await validateSelfie(file);
      setIsValidating(false);

      if (isValid) {
        setValidationSuccess(true);
        setValidationMessage("Verificación de identidad exitosa.");
      } else {
        if (setError) setError("La verificación de identidad falló. Por favor, intente nuevamente.");
      }
    } catch (err) {
      console.error("Error en validación del selfie:", err);
      setIsValidating(false);
      if (setError) setError("Error al procesar el video selfie. Por favor, intente nuevamente.");
    }
  };

  // Function to handle "Submit" button
  const handleSubmit = () => {
    // Only proceed if we have a selfie and either don't need validation or validation was successful
    if (selfiePhoto && (!processId || !validateSelfie || validationSuccess)) {
      onSubmit();
    } else {
      console.log("No se puede continuar: faltan requisitos de validación");
    }
  };

  // Open preview modal
  const openPreviewModal = () => {
    if (previewVideoUrl) {
      setShowPreviewModal(true);
    }
  };

  // Calculated value to check if the "Submit" button should be disabled
  const isSubmitButtonDisabled = Boolean(
    !selfiePhoto ||
    isValidating ||
    Boolean(error) ||
    (processId && validateSelfie && !validationSuccess)
  );

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-5 rounded-xl border border-blue-100">
        <div className="flex items-center space-x-3 mb-2">
          <div className="bg-indigo-100 p-2 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-indigo-800">Video Selfie</h3>
        </div>
        <p className="text-gray-700 text-sm ml-11 mb-3">
          Por último, necesitamos un breve video selfie para verificar tu identidad
        </p>
      </div>

      {/* Success message */}
      {validationSuccess && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-green-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
          {validationMessage || "Verificación de identidad exitosa."}
        </div>
      )}

      {/* Validation error */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500 flex-shrink-0"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
            <span>{error}</span>
          </div>

          {/* Video preview button */}
          <div className="mt-3 ml-7">
            <button
              onClick={openLastVideoPreview}
              className="text-blue-600 hover:text-blue-800 flex items-center text-sm"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              Ver video enviado
            </button>
          </div>
        </div>
      )}

      {/* Camera selection screen */}
      {isCameraSelectionOpen && !isCameraActive && (
        <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm space-y-6">
          <div className="text-center">
            <h4 className="text-lg font-medium text-gray-800 mb-4">Selecciona la cámara que deseas utilizar</h4>

            {!cameraPermissionGranted ? (
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="animate-spin w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full"></div>
                <p className="text-gray-600">Solicitando permisos de cámara...</p>
              </div>
            ) : videoDevices.length === 0 ? (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 text-yellow-800">
                <p>No se detectaron cámaras. Por favor, verifica que tu dispositivo tenga una cámara conectada.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4 max-w-md mx-auto">
                  {videoDevices.map((device) => (
                    <button
                      key={device.deviceId}
                      onClick={() => selectCamera(device.deviceId)}
                      className={cn(
                        "p-4 rounded-lg border transition-colors flex items-center space-x-3",
                        selectedDeviceId === device.deviceId
                          ? "border-indigo-300 bg-indigo-50"
                          : "border-gray-200 hover:border-indigo-200 hover:bg-indigo-50/50"
                      )}
                    >
                      <div className="bg-indigo-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        </svg>
                      </div>
                      <div className="text-left">
                        <p className="font-medium text-gray-800">{device.label}</p>
                      </div>
                    </button>
                  ))}
                </div>

                <div className="flex justify-center mt-4">
                  <Button
                    onClick={() => selectCamera(selectedDeviceId || videoDevices[0].deviceId)}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white"
                    disabled={videoDevices.length === 0}
                  >
                    Utilizar esta cámara
                  </Button>
                </div>
              </div>
            )}

            <div className="flex justify-center mt-4">
              <Button
                onClick={() => setIsCameraSelectionOpen(false)}
                variant="outline"
                className="text-gray-600"
              >
                Cancelar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Video recording area */}
      {isCameraActive && !isCameraSelectionOpen && (
        <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm space-y-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="relative w-full max-w-md aspect-[3/4] bg-gray-100 rounded-lg overflow-hidden">
              {/* Conditional rendering for camera vs recorded video */}
              {!recordingComplete ? (
                <Webcam
                  frontCamera={true}
                  mirrored={true}
                  className="absolute inset-0 w-full h-full object-cover"
                  videoConstraints={{
                    deviceId: selectedDeviceId ? { exact: selectedDeviceId } : undefined,
                    facingMode: "user",
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                  }}
                >
                  {({ getSnapshot, videoElement }) => {
                    getSnapshotRef.current = getSnapshot;
                    videoElementRef.current = videoElement;

                    // If we have the video element, we can get its stream
                    if (videoElement && videoElement.srcObject && !streamRef.current) {
                      streamRef.current = videoElement.srcObject as MediaStream;
                      startFaceCenteringCheck();
                    }

                    return null;
                  }}
                </Webcam>
              ) : (
                <video
                  ref={videoRef}
                  className="absolute inset-0 w-full h-full object-cover"
                  playsInline
                  controls
                  muted
                />
              )}

              {/* Improved face guide with feedback */}
              {!recordingComplete && (
                <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
                  <div className={cn(
                    "w-48 h-48 border-2 rounded-full flex items-center justify-center transition-colors",
                    isFaceCentered
                      ? "border-green-400 border-solid"
                      : "border-yellow-400 border-dashed"
                  )}>
                    {!isFaceCentered && (
                      <div className="bg-yellow-100 bg-opacity-80 text-yellow-800 px-3 py-1 rounded text-sm font-medium">
                        Centra tu rostro
                      </div>
                    )}
                    {isFaceCentered && !isRecording && !countdown && (
                      <div className="bg-green-100 bg-opacity-80 text-green-800 px-3 py-1 rounded text-sm font-medium">
                        ¡Perfecto!
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Recording indicator */}
              {isRecording && (
                <div className="absolute top-4 right-4 flex items-center space-x-2 bg-black bg-opacity-60 text-white px-3 py-1 rounded-full">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">{recordedSeconds}s</span>
                </div>
              )}

              {/* Countdown */}
              {countdown !== null && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40">
                  <div className="text-6xl font-bold text-white">{countdown}</div>
                </div>
              )}

              {/* Validation indicator */}
              {isValidating && (
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                  <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                    <div className="animate-spin w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p className="text-gray-800">Verificando identidad...</p>
                  </div>
                </div>
              )}

              {/* Success indicator */}
              {validationSuccess && (
                <div className="absolute bottom-3 right-3 bg-green-500 text-white p-2 rounded-full shadow-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* User instructions */}
            {!recordingComplete && !isRecording && !countdown && (
              <div className="bg-blue-50 border border-blue-100 rounded-md p-3 text-sm text-blue-800 max-w-md">
                <p className="font-medium mb-2">Para una verificación exitosa:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Asegúrate de estar en un lugar bien iluminado</li>
                  <li>Centra tu rostro en el círculo hasta que se vuelva verde</li>
                  <li>Mantén una expresión neutral y mira directamente a la cámara</li>
                  <li>Quítate gafas, gorras u otros accesorios que cubran tu rostro</li>
                </ul>
              </div>
            )}

            <div className="flex flex-wrap justify-center gap-3 mt-4">
              {!recordingComplete ? (
                isRecording ? (
                  <Button
                    onClick={stopRecording}
                    variant="outline"
                    className="flex items-center gap-2 border-red-300 text-red-700 hover:bg-red-50"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
                    </svg>
                    Detener Grabación
                  </Button>
                ) : (
                  <Button
                    onClick={prepareRecording}
                    className={cn(
                      "flex items-center gap-2 text-white",
                      isFaceCentered ? "bg-red-600 hover:bg-red-700" : "bg-gray-400 cursor-not-allowed"
                    )}
                    disabled={!isFaceCentered || !!countdown}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Iniciar Grabación
                  </Button>
                )
              ) : (
                <Button
                  onClick={prepareRecording}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Volver a Grabar
                </Button>
              )}

              <Button
                onClick={cancelRecording}
                variant="outline"
                className="flex items-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Cancelar
              </Button>

              {!isRecording && !recordingComplete && (
                <Button
                  onClick={() => setIsCameraSelectionOpen(true)}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Cambiar Cámara
                </Button>
              )}

              {/* Preview button */}
              {recordingComplete && previewVideoUrl && (
                <Button
                  onClick={openPreviewModal}
                  variant="outline"
                  className="flex items-center gap-2 border-blue-300 text-blue-700 hover:bg-blue-50"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Ver en pantalla completa
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Visual guide and message for video recording */}
      {!isCameraActive && !isCameraSelectionOpen && !selfiePhoto && (
        <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm space-y-6">
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 flex flex-col items-center">
            <div className="w-40 h-40 bg-gray-200 rounded-full overflow-hidden flex items-center justify-center mb-4 relative">
              <div className="absolute inset-4 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>

            <ul className="text-sm text-gray-600 space-y-1 w-full max-w-md">
              <li className="flex items-start space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Asegúrate de estar en un lugar bien iluminado</span>
              </li>
              <li className="flex items-start space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Tu rostro debe estar completamente visible</span>
              </li>
              <li className="flex items-start space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Quítate gafas, gorras o cualquier accesorio que oculte tu rostro</span>
              </li>
            </ul>
          </div>

          <div className="relative">
            <div className="border-2 border-dashed rounded-lg p-8 text-center transition-colors border-indigo-300 bg-indigo-50">
              <div className="space-y-3">
                <div className="flex items-center justify-center text-indigo-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <p className="text-indigo-700 font-medium">Grabar video selfie</p>
                <p className="text-indigo-500 text-sm">Debes grabar un breve video de 5 segundos con tu rostro</p>
                <div className="flex flex-wrap justify-center gap-3">
                  <button
                    className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors font-medium text-sm"
                    onClick={activateCamera}
                  >
                    Seleccionar cámara y grabar
                  </button>
                </div>
                <p className="text-gray-500 text-xs mt-4">Para verificar tu identidad, es necesario grabar un nuevo video en este momento.</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View of already recorded selfie */}
      {selfiePhoto && !isCameraActive && !isCameraSelectionOpen && (
        <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm space-y-6">
          <div className="flex items-center justify-center">
            {/* Video thumbnail */}
            <div className="relative w-64 h-64 bg-gray-100 rounded-lg overflow-hidden border border-gray-200">
              {/* Create a new URL object for this view to ensure it's fresh */}
              <video
                src={videoUrlRef.current || (selfiePhoto ? URL.createObjectURL(selfiePhoto) : undefined)}
                className="w-full h-full object-cover"
                controls
                muted
                onError={(e) => {
                  console.error("Error in preview video:", e);
                  if (setError) setError("Error al mostrar el video grabado. Intente grabar nuevamente.");
                }}
              />

              {validationSuccess && (
                <div className="absolute bottom-3 right-3 bg-green-500 text-white p-2 rounded-full shadow-lg">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              )}
            </div>
          </div>

          {/* Information and actions */}
          <div className="flex flex-col items-center space-y-4">
            <div className="bg-green-50 border border-green-200 p-4 rounded-md text-green-800 max-w-md">
              <p className="font-medium">Video selfie capturado correctamente</p>
              <p className="text-sm mt-1">{(selfiePhoto.size / 1024 / 1024).toFixed(2)} MB • {selfiePhoto.type}</p>
            </div>

            <div className="flex flex-wrap justify-center gap-3">
              <Button
                onClick={activateCamera}
                variant="outline"
                className="flex items-center gap-2"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Grabar nuevamente
              </Button>

              {/* Button to open preview modal */}
              {previewVideoUrl && (
                <Button
                  onClick={openPreviewModal}
                  variant="outline"
                  className="flex items-center gap-2 border-blue-300 text-blue-700 hover:bg-blue-50"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Ver en pantalla completa
                </Button>
              )}

              {validationSuccess && (
                <div className="inline-flex items-center px-4 py-2 bg-green-100 text-green-700 rounded-md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  Verificado
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between pt-4">
        <Button
          onClick={onPrevious}
          variant="outline"
          size="lg"
          className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
          disabled={isValidating}
          type="button"
        >
          Anterior
        </Button>

        <Button
          onClick={handleSubmit}
          className={`w-full max-w-[140px] ${validationSuccess
              ? "bg-green-600 hover:bg-green-700"
              : "bg-indigo-600 hover:bg-indigo-700"
            } text-white`}
          size="lg"
          disabled={isSubmitButtonDisabled}
          type="button"
        >
          {validationSuccess ? (
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Enviar
            </div>
          ) : (
            "Enviar Solicitud"
          )}
        </Button>
      </div>

      {/* Preview Modal */}
      {showPreviewModal && previewVideoUrl && (
        <VideoPreviewModal
          videoUrl={previewVideoUrl}
          filename={previewFilename}
          processId={processId || null}
          onClose={() => setShowPreviewModal(false)}
        />
      )}
    </div>
  );
}

// Helper type for MediaRecorder options
interface MediaRecorderOptions {
  mimeType: string;
  videoBitsPerSecond: number;
}