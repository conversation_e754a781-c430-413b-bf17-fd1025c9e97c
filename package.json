{"name": "creddigital-onboarding", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@types/leaflet": "^1.9.18", "@types/react-signature-canvas": "^1.0.7", "@webcam/react": "^1.0.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "leaflet": "^1.9.4", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-router-dom": "^7.5.2", "react-signature-canvas": "^1.1.0-alpha.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/vite": "^4.1.4", "@types/node": "^22.15.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}