# Estructura de Directorios del Proyecto

Este documento explica la estructura de directorios del proyecto, basada en un patrón de **Arquitectura Basada en Características (Feature-Based Architecture)**.

## 📁 /src

Directorio raíz que contiene todo el código fuente de la aplicación.

### 📁 /assets

**Propósito**: Almacenar recursos estáticos como imágenes, iconos, fuentes, etc.

**Cómo se usa**:
- Los archivos se importan directamente en los componentes donde se necesitan
- Facilita la gestión centralizada de recursos visuales
- Se puede organizar en subdirectorios (images, icons, fonts) para proyectos grandes

**Ejemplo**:
```tsx
import logo from '@/assets/logo.svg';

function Header() {
  return <img src={logo} alt="Logo de la empresa" />;
}
```

### 📁 /components

**Propósito**: Contener componentes reutilizables en toda la aplicación.

**Cómo se usa**:
- Se organizan por tipo o dominio (UI, layout, forms, etc.)
- Son componentes "tontos" (sin lógica de negocio compleja)
- Altamente reutilizables y con responsabilidades bien definidas

**Subdirectorios**:

#### 📁 /components/Layout

**Propósito**: Contiene componentes relacionados con la estructura general de la aplicación.

**Ejemplo**: `Layout.tsx` que define el contenedor principal de la aplicación.

#### 📁 /components/ui

**Propósito**: Componentes UI básicos, generalmente integrados de shadcn/ui.

**Interacción**: Estos componentes son utilizados por componentes más complejos y páginas.

**Ejemplo de uso**:
```tsx
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

function LoginForm() {
  return (
    <form>
      <Input placeholder="Email" />
      <Button>Iniciar sesión</Button>
    </form>
  );
}
```

### 📁 /config

**Propósito**: Almacenar archivos de configuración de la aplicación.

**Cómo se usa**:
- Variables de entorno tipadas
- Configuración de APIs
- Opciones de tema o internacionalización

**Ejemplo**:
```tsx
// config/api.ts
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://api.ejemplo.com';
export const API_TIMEOUT = 10000;
```

### 📁 /constants

**Propósito**: Definir constantes y enumeraciones utilizadas en la aplicación.

**Cómo se usa**:
- Valores que no cambian y se utilizan en múltiples lugares
- Ayuda a evitar "números mágicos" o strings duplicadas en el código

**Ejemplo**:
```tsx
// constants/creditLimits.ts
export const MIN_CREDIT_AMOUNT = 5000;
export const MAX_CREDIT_AMOUNT = 100000;
export const DEFAULT_CREDIT_AMOUNT = 25000;
```

### 📁 /context

**Propósito**: Contener los Context Providers de React para manejo de estado global.

**Cómo se usa**:
- Implementa la Context API de React
- Gestiona estados que necesitan ser accesibles desde múltiples componentes

**Ejemplo**:
```tsx
// context/CreditApplicationContext.tsx
import { createContext, useState } from 'react';

export const CreditApplicationContext = createContext(null);

export function CreditApplicationProvider({ children }) {
  const [applicationData, setApplicationData] = useState({});
  
  return (
    <CreditApplicationContext.Provider value={{ applicationData, setApplicationData }}>
      {children}
    </CreditApplicationContext.Provider>
  );
}
```

### 📁 /features

**Propósito**: Contiene módulos organizados por características o dominios de negocio.

**Cómo se usa**:
- Cada característica encapsula su propia lógica, componentes y estado
- Facilita trabajar en equipos y mantener el código organizado

**Subdirectorios**:

#### 📁 /features/credit

**Propósito**: Componentes y lógica específica para la funcionalidad de solicitud de crédito.

**Cómo se organiza**:
- Puede tener sus propios componentes, hooks y utilidades
- Se enfoca en la lógica de negocio relacionada con créditos

**Ejemplo de estructura**:
```
/features/credit
  /components       # Componentes específicos de crédito
    CreditStepper.tsx
    AmountSelector.tsx
  /hooks            # Hooks específicos de crédito
    useCreditCalculator.ts
  /utils            # Utilidades específicas
    creditValidation.ts
  index.ts          # Exporta los componentes/hooks principales
```

### 📁 /hooks

**Propósito**: Contiene hooks personalizados reutilizables.

**Cómo se usa**:
- Encapsula lógica reutilizable
- Simplifica componentes al extraer lógica compleja

**Ejemplo**:
```tsx
// hooks/useForm.ts
import { useState } from 'react';

export function useForm(initialValues) {
  const [values, setValues] = useState(initialValues);
  
  const handleChange = (e) => {
    setValues({ ...values, [e.target.name]: e.target.value });
  };
  
  return { values, handleChange };
}
```

### 📁 /lib

**Propósito**: Contiene funciones utilitarias y bibliotecas.

**Cómo se usa**:
- Funciones generales que no pertenecen a un dominio específico
- Wrappers para APIs externas o bibliotecas

**Ejemplo**: `utils.ts` con funciones como `formatCurrency`.

### 📁 /pages

**Propósito**: Contiene componentes de página que corresponden a rutas específicas.

**Cómo se usa**:
- Cada archivo representa una página/ruta en la aplicación
- Compone componentes de las carpetas `/components` y `/features`
- Maneja la lógica específica de la página

**Organización**:
- Subdirectorios para páginas relacionadas
- Archivos de componentes principales y componentes específicos de la página

**Separación de componentes**:
Para separar componentes como el Stepper de la página principal:

1. Crea el componente en el directorio apropiado:
   ```
   /features/credit/components/CreditStepper.tsx
   ```

2. Importa y usa el componente en la página:
   ```tsx
   // pages/application/ApplicationPage.tsx
   import { CreditStepper } from '@/features/credit/components/CreditStepper';
   
   export default function ApplicationPage() {
     return (
       <div>
         <h1>Solicitud de crédito</h1>
         <CreditStepper />
       </div>
     );
   }
   ```

**Ejemplo de estructura**:
```
/pages
  /welcome
    WelcomePage.tsx
    WelcomeInfo.tsx       # Componente usado solo en esta página
  /terms
    TermsPage.tsx
  /application
    ApplicationPage.tsx
    ApplicationSummary.tsx # Componente específico de esta página
```

### 📁 /routes

**Propósito**: Configuración y lógica del enrutamiento de la aplicación.

**Cómo se usa**:
- Define las rutas de la aplicación
- Implementa lógica de protección de rutas o redirecciones

**Ejemplo**:
```tsx
// routes/index.tsx
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from "@/components/Layout/Layout";
import WelcomePage from "@/pages/welcome/WelcomePage";
import TermsPage from "@/pages/terms/TermsPage";
import ApplicationPage from "@/pages/application/ApplicationPage";

export default function AppRoutes() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<WelcomePage />} />
          <Route path="terms" element={<TermsPage />} />
          <Route path="application" element={<ApplicationPage />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
```

### 📁 /services

**Propósito**: Servicios para comunicación con APIs o lógica de negocio compleja.

**Cómo se usa**:
- Encapsula todas las llamadas a APIs
- Abstraye la lógica de comunicación con servicios externos

**Ejemplo**:
```tsx
// services/creditApi.ts
import axios from 'axios';
import { API_BASE_URL } from '@/config/api';

export async function submitCreditApplication(data) {
  return axios.post(`${API_BASE_URL}/credit/applications`, data);
}
```

### 📁 /styles

**Propósito**: Estilos globales y configuración de temas.

**Cómo se usa**:
- Estilos CSS/SCSS globales
- Configuración de temas para Tailwind CSS
- Variables de diseño compartidas

### 📁 /types

**Propósito**: Definiciones de tipos TypeScript compartidos.

**Cómo se usa**:
- Interfaces y tipos reutilizables
- Ayuda a mantener la consistencia de tipos en toda la aplicación

**Ejemplo**:
```tsx
// types/creditApplication.ts
export interface CreditApplication {
  amount: number;
  term: number;
  nationalId: string;
  deliveryMethod: 'app' | 'branch';
}
```

## 🔄 Flujo de Datos

El flujo típico de datos en esta arquitectura es:

1. **Páginas** renderiza componentes de alto nivel
2. Las páginas utilizan componentes de **features** para la lógica de dominio
3. Los componentes de **features** utilizan componentes de **components/ui** para la interfaz
4. Los **hooks** y **services** proveen funcionalidad y datos
5. **Context** maneja estado global cuando es necesario

## 📋 Separación de Componentes (Ejemplo - Stepper)

Para extraer el componente Stepper del código actual:

1. Crea el componente en **features/credit/components**:

```tsx
// features/credit/components/CreditStepper.tsx
interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

export function CreditStepper({ currentStep, totalSteps }: StepIndicatorProps) {
  return (
    <div className="w-full mb-8">
      <div className="flex justify-between mb-2">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <div 
            key={index}
            className={`flex flex-col items-center`}
          >
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                index < currentStep 
                  ? "bg-blue-600 text-white" 
                  : index === currentStep 
                  ? "bg-blue-100 border-2 border-blue-600 text-blue-600" 
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              {index + 1}
            </div>
            <span className="text-xs mt-1 text-gray-500">
              {index === 0 && "Monto"}
              {index === 1 && "Plazo"}
              {index === 2 && "ID"}
              {index === 3 && "Entrega"}
            </span>
          </div>
        ))}
      </div>
      <div className="relative h-1 bg-gray-200 mt-2">
        <div 
          className="absolute h-1 bg-blue-600 transition-all duration-300"
          style={{ width: `${(currentStep / (totalSteps - 1)) * 100}%` }}
        ></div>
      </div>
    </div>
  );
}
```

2. Usa el componente en la página:

```tsx
// pages/application/ApplicationPage.tsx
import { CreditStepper } from "@/features/credit/components/CreditStepper";

export default function ApplicationPage() {
  const [currentStep, setCurrentStep] = useState(0);
  
  // ... resto del código
  
  return (
    <div className="flex flex-col min-h-screen p-6">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Solicitud de Crédito</h1>
      
      <CreditStepper currentStep={currentStep} totalSteps={4} />
      
      {renderStep()}
    </div>
  );
}
```

## 🤔 ¿Por qué esta estructura?

Esta estructura basada en características:

1. **Facilita el crecimiento** - A medida que la aplicación crece, es fácil añadir nuevas características
2. **Favorece la modularidad** - Cada parte del código tiene un propósito claro
3. **Mejora la colaboración** - Los equipos pueden trabajar en diferentes características sin pisarse
4. **Simplifica el mantenimiento** - Los problemas pueden aislarse y resolverse más fácilmente
5. **Promueve la reutilización** - Los componentes y la lógica están diseñados para ser reutilizables