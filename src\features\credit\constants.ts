// Definir un tipo para los códigos de país
export type CountryCode = 'GT' | 'SV' | 'HN' | 'NI' | 'CR' | 'PA' | 'MX';

// Agregar esta constante para los códigos de país
export const COUNTRY_CODES: Record<CountryCode, string> = {
  GT: "502", // Guatemala
  SV: "503", // El Salvador
  HN: "504", // Honduras
  NI: "505", // Nicaragua
  CR: "506", // Costa Rica
  PA: "507", // Panamá
  MX: "52",  // México
};

// País por defecto (se puede mover a .env si es necesario)
export const DEFAULT_COUNTRY: CountryCode = "GT";

// Credit application constants
export const CREDIT_AMOUNT = {
  MIN: 1000,
  MAX: 50000,
  DEFAULT: 20000,
  POPULAR: 20000, // El monto más pedido por los clientes
  STEP: 1000
};

export const LOAN_TERM = {
  MIN: 1,
  MAX: 32,
  DEFAULT: 16,
  STEP: 1
};

export const DELIVERY_METHODS = {
  BRANCH: 'sucursal',
  ATM: 'cajero',
  TRANSFER: 'transferencia'
};

// Step labels for the application process
export const STEP_LABELS = ["Solicitud", "Datos Cliente", "Fotografías", "Datos Financieros", "Referencias"];

// STEP 2 - Client data constants
export const NAME_LASTNAME = {
  MIN: 1,
  MAX: 35,
  DEFAULT: 0,
  STEP: 2
};

export const PHONE_NUMBER = {
  MIN: 9,
  MAX: 11,
  DEFAULT: 0,
  STEP: 2
};




