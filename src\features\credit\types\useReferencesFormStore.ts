import { create } from 'zustand';

// Tipos de parentesco
export type Parentesco = 'Familiar' | 'Amistad' | 'Laboral' | 'Cónyuge';

// Modelo de una referencia
export interface Reference {
  fullName: string;
  phone: string;
  address: string;
  relationship: Parentesco | '';
}

// Referencia base vacía
const createInitialReference = (): Reference => ({
  fullName: '',
  phone: '',
  address: '',
  relationship: '',
});

// Grupo inicial de 3 referencias (la tercera es el cónyuge)
const createInitialReferences = (): [Reference, Reference, Reference] => ([
  createInitialReference(),
  createInitialReference(),
  { ...createInitialReference(), relationship: 'Cónyuge' }
]);

// Definición del estado global
export interface ReferencesFormState {
  references: [Reference, Reference, Reference];
  signatureImage: File | null;

  setReferenceField: (
    index: 0 | 1 | 2,
    field: keyof Reference,
    value: string
  ) => void;

  setSignatureImage: (file: File | null) => void;

  reset: () => void;
}

// Store Zustand
export const useReferencesFormStore = create<ReferencesFormState>((set) => ({
  references: createInitialReferences(),
  signatureImage: null,

  setReferenceField: (index, field, value) =>
    set((state) => {
      const updated = [...state.references] as [Reference, Reference, Reference];
      updated[index] = {
        ...updated[index],
        [field]: value,
      };
      return { references: updated };
    }),

  setSignatureImage: (file) =>
    set(() => ({ signatureImage: file })),

  reset: () => set(() => ({
    references: createInitialReferences(),
    signatureImage: null
  })),
}));
