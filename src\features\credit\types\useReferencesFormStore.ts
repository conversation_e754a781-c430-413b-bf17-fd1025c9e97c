import { create } from 'zustand';

export type Parentesco = 'Familiar' | 'Amistad' | 'Laboral' | 'Cónyuge';

export interface Reference {
  fullName: string;
  phone: string;
  address: string;
  relationship: Parentesco | '';
}

const initialReference: Reference = {
  fullName: '',
  phone: '',
  address: '',
  relationship: '',
};

export interface ReferencesFormState {
  references: [Reference, Reference, Reference]; // Ahora son 3 referencias

  setReferenceField: (
    index: 0 | 1 | 2, // Actualizamos para incluir el índice 2
    field: keyof Reference,
    value: string
  ) => void;

  reset: () => void;
}

export const useReferencesFormStore = create<ReferencesFormState>((set) => ({
  references: [ 
    { ...initialReference }, 
    { ...initialReference },
    { ...initialReference, relationship: 'Cónyuge' } // La tercera referencia es para el cónyuge
  ],

  setReferenceField: (index, field, value) =>
    set((state) => {
      const updated = [...state.references] as [Reference, Reference, Reference];
      updated[index] = {
        ...updated[index],
        [field]: value,
      };
      return { references: updated };
    }),

  reset: () => set(() => ({
    references: [ 
      { ...initialReference }, 
      { ...initialReference },
      { ...initialReference, relationship: 'Cónyuge' }
    ],
  })),
}));
