import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { FrontIdComponent } from "./photographs/FrontIdComponent";
import { BackIdComponent } from "./photographs/BackIdComponent";
import { SelfieComponent } from "./photographs/SelfieComponent";
import { useVerificationStore } from "@/stores";

/**
 * Props para el componente de fotografías
 */
interface PhotographsStepProps {
  onSubmit: () => void;
  onPrevious: () => void;
}

/**
 * Componente principal para el paso de carga de fotografías
 */
export function PhotographsStep({ onSubmit, onPrevious }: PhotographsStepProps) {
  // Get values and functions from the verification store
  const { 
    nationalId,
    processId,
    error,
    isLoading,
    frontIdPhoto: idFrontPhoto,
    backIdPhoto: idBackPhoto, 
    selfiePhoto,
    setFrontIdPhoto: setIdFrontPhoto,
    setBackIdPhoto: setIdBackPhoto,
    setSelfiePhoto,
    setStage: setStoreStage,
    validateFrontDpi,
    validateBackDpi,
    validateSelfie,
    uploadFrontId,
    uploadBackId,
    uploadSelfie,
    completeVerification,
    initVerificationProcess
  } = useVerificationStore();
  
  // Estado para controlar las etapas del proceso de fotografía
  const [stage, setStage] = useState<'welcome' | 'front' | 'back' | 'selfie'>('welcome');
  
  // Estado para la aceptación de la política de privacidad
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  
  // Estado para simular la solicitud de permisos de cámara
  const [requestingPermission, setRequestingPermission] = useState(false);
  
  // Estado para rastrear cuando estamos subiendo una imagen
  const [isUploading, setIsUploading] = useState(false);
  
  // Estado local para errores específicos del componente (para mantener errores que no vienen de la store)
  const [localError, setLocalError] = useState<string | null>(null);
  
  // Función para iniciar el proceso de fotografía
  const startPhotoProcess = async () => {
    if (privacyAccepted) {
      setRequestingPermission(true);
      setLocalError(null);
      
      try {
        console.log("Iniciando proceso de verificación con ID:", nationalId);
        // Use the store's initVerificationProcess which uses the nationalId from the store
        const success = await initVerificationProcess();
        
        if (success) {
          console.log("Proceso iniciado correctamente, ID de proceso:", processId);
          setRequestingPermission(false);
          setStage('front');
          setStoreStage('front');
        } else {
          console.error("No se pudo iniciar el proceso, error:", error);
          setRequestingPermission(false);
        }
      } catch (err) {
        console.error("Error al iniciar el proceso:", err);
        setLocalError("Ha ocurrido un error inesperado. Por favor, intenta nuevamente.");
        setRequestingPermission(false);
      }
    }
  };
  
  // Funciones de navegación entre etapas
  const goToFrontIdStage = () => {
    setStage('front');
    setStoreStage('front');
  };
  
  const goToBackIdStage = () => {
    console.log("Navegando a la etapa de reverso del DPI");
    setStage('back');
    setStoreStage('back');
  };
  
  const goToSelfieStage = () => {
    console.log("Navegando a la etapa de selfie");
    setStage('selfie');
    setStoreStage('selfie');
  };
  
  const handlePrevious = () => {
    if (stage === 'front') {
      setStage('welcome');
      setStoreStage('welcome');
    } else if (stage === 'back') {
      setStage('front');
      setStoreStage('front');
    } else if (stage === 'selfie') {
      setStage('back');
      setStoreStage('back');
    } else {
      onPrevious();
    }
  };
  
  // Funciones de manejo para cada etapa
  const handleFrontNext = async () => {
    console.log("Front Next Button Clicked - SKIPPING UPLOAD FOR NOW");
    
    // IMPORTANT: For development/testing purposes, skip the upload
    // and just proceed to the next stage to avoid 404 errors
    goToBackIdStage();
    return;
    
    /* 
    // This code is commented out until the API endpoint for uploads is fixed
    if (idFrontPhoto) {
      setIsUploading(true);
      setLocalError(null);
      
      try {
        console.log("Preparando para subir la imagen frontal del DPI...");
        // Log the image details for debugging
        console.log("Imagen:", idFrontPhoto.name, idFrontPhoto.type, idFrontPhoto.size);
        
        const uploaded = await uploadFrontId();
        setIsUploading(false);
        
        if (uploaded) {
          console.log("Imagen frontal subida correctamente");
          goToBackIdStage();
        } else {
          console.error("Error al subir imagen frontal:", error);
          setLocalError("No se pudo subir la imagen del frente del DPI. Por favor, intenta nuevamente.");
        }
      } catch (err) {
        console.error("Error al subir imagen frontal:", err);
        setLocalError("Error al subir la imagen. Por favor, intenta nuevamente.");
        setIsUploading(false);
      }
    } else {
      console.log("No hay imagen frontal, pasando a la siguiente etapa para pruebas");
      goToBackIdStage(); // Para desarrollo, permitir avanzar sin imagen
    }
    */
  };
  
  const handleBackNext = async () => {
    console.log("Back Next Button Clicked - SKIPPING UPLOAD FOR NOW");
    
    // IMPORTANT: For development/testing purposes, skip the upload
    // and just proceed to the next stage to avoid 404 errors
    goToSelfieStage();
    return;
    
    /*
    // This code is commented out until the API endpoint for uploads is fixed
    if (idBackPhoto) {
      setIsUploading(true);
      setLocalError(null);
      
      try {
        console.log("Preparando para subir la imagen trasera del DPI...");
        const uploaded = await uploadBackId();
        setIsUploading(false);
        
        if (uploaded) {
          console.log("Imagen trasera subida correctamente");
          goToSelfieStage();
        } else {
          console.error("Error al subir imagen trasera:", error);
          setLocalError("No se pudo subir la imagen del reverso del DPI. Por favor, intenta nuevamente.");
        }
      } catch (err) {
        console.error("Error al subir imagen trasera:", err);
        setLocalError("Error al subir la imagen. Por favor, intenta nuevamente.");
        setIsUploading(false);
      }
    } else {
      console.log("No hay imagen trasera, pasando a la siguiente etapa para pruebas");
      goToSelfieStage(); // Para desarrollo, permitir avanzar sin imagen
    }
    */
  };
  
  const handleSelfieSubmit = async () => {
    console.log("Selfie Submit Button Clicked - SKIPPING UPLOAD FOR NOW");
    
    // IMPORTANT: For development/testing purposes, skip the upload
    // and just proceed to the next stage to avoid 404 errors
    onSubmit();
    return;
    
    /*
    // This code is commented out until the API endpoint for uploads is fixed
    if (selfiePhoto) {
      setIsUploading(true);
      setLocalError(null);
      
      try {
        console.log("Preparando para subir la imagen de selfie...");
        const uploaded = await uploadSelfie();
        setIsUploading(false);
        
        if (uploaded) {
          console.log("Imagen de selfie subida correctamente");
          const completed = await completeVerification();
          
          if (completed) {
            console.log("Proceso de verificación completado correctamente");
            onSubmit();
          } else {
            console.error("Error al completar el proceso:", error);
            setLocalError("No se pudo completar el proceso de verificación. Por favor, intenta nuevamente.");
          }
        } else {
          console.error("Error al subir selfie:", error);
          setLocalError("No se pudo subir la imagen de selfie. Por favor, intenta nuevamente.");
        }
      } catch (err) {
        console.error("Error al subir selfie:", err);
        setLocalError("Error al subir la imagen. Por favor, intenta nuevamente.");
        setIsUploading(false);
      }
    } else {
      console.log("No hay imagen de selfie, completando el proceso para pruebas");
      
      try {
        const completed = await completeVerification();
        
        if (completed) {
          console.log("Proceso de verificación completado correctamente");
          onSubmit();
        } else {
          console.error("Error al completar el proceso:", error);
          setLocalError("No se pudo completar el proceso de verificación. Por favor, intenta nuevamente.");
        }
      } catch (err) {
        console.error("Error al completar verificación:", err);
        setLocalError("Error al completar el proceso. Por favor, intenta nuevamente.");
      }
    }
    */
  };
  
  // Handler for updating local state when we want to set an error from components
  const setErrorWrapper = (errorMessage: string | null) => {
    setLocalError(errorMessage);
  };
  
  // Renderizar la pantalla de bienvenida
  const renderWelcomeScreen = () => (
    <div className="space-y-6">
      {(error || localError) && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
          {error || localError}
        </div>
      )}
      
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100 shadow-sm">
        <div className="flex items-center space-x-4 mb-4">
          <div className="bg-indigo-100 p-3 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-indigo-800">Verificación de identidad</h3>
        </div>
        
        <p className="text-gray-700 mb-4">
          A continuación, necesitaremos que cargues fotografías de tus documentos de identidad y grabes un breve video selfie para verificar tu identidad.
        </p>
        
        {/* Display National ID for verification */}
        <div className="bg-yellow-50 border border-yellow-200 p-3 rounded-md mb-4">
          <p className="text-gray-700 font-medium">Número de identificación: <span className="font-bold">{nationalId}</span></p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm text-center">
            <div className="bg-blue-100 p-3 rounded-full w-14 h-14 mx-auto mb-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-800 mb-1">Parte frontal del DPI</h4>
            <p className="text-xs text-gray-500">Fotografía clara y completa</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm text-center">
            <div className="bg-blue-100 p-3 rounded-full w-14 h-14 mx-auto mb-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-800 mb-1">Parte trasera del DPI</h4>
            <p className="text-xs text-gray-500">Fotografía clara y completa</p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm text-center">
            <div className="bg-blue-100 p-3 rounded-full w-14 h-14 mx-auto mb-2 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 017.072 0m-9.9-2.828a9 9 0 0112.728 0" />
              </svg>
            </div>
            <h4 className="font-semibold text-gray-800 mb-1">Video selfie</h4>
            <p className="text-xs text-gray-500">Breve grabación de tu rostro</p>
          </div>
        </div>
        
        <div className="border-t border-gray-200 pt-4">
          <label className="flex items-start space-x-3 py-2 cursor-pointer group">
            <input
              type="checkbox"
              checked={privacyAccepted}
              onChange={() => setPrivacyAccepted(!privacyAccepted)}
              className="mt-1 h-4 w-4 text-indigo-600 transition duration-150 ease-in-out border-gray-300 rounded focus:ring-indigo-500"
            />
            <span className="text-sm text-gray-600 group-hover:text-gray-800 transition-colors">
              He leído y acepto las políticas de privacidad. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam posuere lectus non mi porttitor, non finibus dui faucibus. Aliquam erat volutpat. Vestibulum ac dictum metus, at gravida elit.
            </span>
          </label>
        </div>
      </div>
      
      <div className="flex justify-between mt-6">
        <Button
          onClick={onPrevious}
          variant="outline"
          className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
          size="lg"
          type="button"
        >
          Regresar
        </Button>
        <Button
          onClick={startPhotoProcess}
          disabled={!privacyAccepted || isLoading || requestingPermission}
          className="w-full max-w-[140px] bg-indigo-600 hover:bg-indigo-700 text-white"
          size="lg"
          type="button"
        >
          {isLoading || requestingPermission ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Solicitando...
            </div>
          ) : (
            'Siguiente'
          )}
        </Button>
      </div>
    </div>
  );
  
  // Renderizar el componente basado en la etapa actual
  const renderContent = () => {
    switch (stage) {
      case 'welcome':
        return renderWelcomeScreen();
      case 'front':
        return (
          <FrontIdComponent 
            idFrontPhoto={idFrontPhoto}
            setIdFrontPhoto={setIdFrontPhoto}
            onNext={handleFrontNext}
            onPrevious={handlePrevious}
            processId={processId}
            validateFrontDpi={validateFrontDpi}
            error={error || localError}
            setError={setErrorWrapper}
          />
        );
      case 'back':
        return (
          <BackIdComponent 
            idBackPhoto={idBackPhoto}
            setIdBackPhoto={setIdBackPhoto}
            onNext={handleBackNext}
            onPrevious={handlePrevious}
            processId={processId}
            validateBackDpi={validateBackDpi}
            error={error || localError}
            setError={setErrorWrapper}
          />
        );
      case 'selfie':
        return (
          <SelfieComponent 
            selfiePhoto={selfiePhoto}
            setSelfiePhoto={setSelfiePhoto}
            onSubmit={handleSelfieSubmit}
            onPrevious={handlePrevious}
            processId={processId}
            validateSelfie={validateSelfie}
            error={error || localError}
            setError={setErrorWrapper}
          />
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-8 pb-8">
      <h2 className="text-xl font-semibold text-indigo-800 pb-2 border-b border-indigo-100">Fotografías</h2>
      
      {/* Mensaje de carga durante la subida de imágenes */}
      {isUploading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="animate-spin w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-gray-800">Subiendo imagen...</p>
          </div>
        </div>
      )}
      
      {/* Barra de progreso */}
      {stage !== 'welcome' && (
        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-indigo-600" 
            style={{ 
              width: stage === 'front' ? '33%' : stage === 'back' ? '66%' : '100%',
              transition: 'width 0.5s ease'
            }}
          ></div>
        </div>
      )}
      
      {renderContent()}
    </div>
  );
}