import { create } from 'zustand';

export interface Branch {
  id: number;
  name: string;
  address: string;
  phone: string;
  hours: string;
  coordinates: [number, number];
  services: string[];
}

export interface BranchState {
  branches: Branch[];
  selectedBranchId: number | null;

  // Actions
  setBranches: (branches: Branch[]) => void;
  selectBranch: (branchId: number | null) => void;
  reset: () => void;
}

export const useBranchStore = create<BranchState>((set) => ({
  branches: [],
  selectedBranchId: null,

  setBranches: (branches) => set({ branches }),
  selectBranch: (branchId) => set({ selectedBranchId: branchId }),
  reset: () => set({ selectedBranchId: null }),
}));
