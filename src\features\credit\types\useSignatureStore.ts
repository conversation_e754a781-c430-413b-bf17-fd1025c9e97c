import { create } from 'zustand';

interface SignatureState {
  signatureDataUrl: string | null;
  setSignatureDataUrl: (dataUrl: string | null) => void;
  reset: () => void;
}

export const useSignatureStore = create<SignatureState>((set) => ({
  signatureDataUrl: null,
  
  setSignatureDataUrl: (dataUrl) => set({ 
    signatureDataUrl: dataUrl 
  }),
  
  reset: () => set({ 
    signatureDataUrl: null 
  }),
}));
