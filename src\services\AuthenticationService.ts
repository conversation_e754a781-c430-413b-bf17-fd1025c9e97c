import axios from 'axios';

/**
 * Configuración para los servicios de autenticación
 */
interface AuthConfig {
  baseUrl: string;
  apiKey: string;
  connectionId: string;
}

/**
 * Respuesta del proceso de inicialización con DPI
 */
interface InitDpiResponse {
  success: boolean;
  processId?: string;
  message?: string;
  error?: string;
  details?: string;
}

/**
 * Datos para iniciar verificación con DPI
 */
interface InitDpiData {
  identificador: string;
  connection: string;
}

/**
 * Servicio para manejar la autenticación y verificación de identidad
 */
class AuthenticationService {
  private config: AuthConfig;
  // Add the property declaration for lastSelfieVideo
  private lastSelfieVideo: {
    url: string;
    file: File;
    filename: string;
  } | null = null;

  constructor(config: AuthConfig) {
    this.config = config;
  }
  
  /**
   * Gets the last selfie video that was processed
   * @returns The last selfie video information or null if none exists
   */
  getLastSelfieVideo(): { url: string; file: File; filename: string } | null {
    return this.lastSelfieVideo;
  }

  /**
   * Inicializa un proceso de verificación usando número de DPI
   * @param dpiNumber - Número de DPI del usuario
   * @returns Promesa con la respuesta del proceso
   */
  async initProcessWithDpiNumber(dpiNumber: string): Promise<InitDpiResponse> {
    try {
      // Usar el dpiNumber proporcionado o el valor hardcodeado si no se proporciona
      const data: InitDpiData = {
        identificador: dpiNumber || "3077107500604", // Valor predeterminado hardcodeado
        connection: this.config.connectionId
      };

      console.log('Sending request with data:', data);

      const response = await axios({
        method: 'POST',
        url: `${this.config.baseUrl}/initProces/api/initWhitDPINumber`,
        headers: {
          'Content-Type': 'application/json',
          'connection-mg': this.config.connectionId,
          'api-key': this.config.apiKey
        },
        data
      });

      console.log('API Response:', response.data);

      return {
        success: !response.data.error,
        processId: response.data.details,
        message: response.data.message,
        details: response.data.details
      };
    } catch (error: any) {
      console.error('Error al iniciar proceso con DPI:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Error desconocido'
      };
    }
  }

  /**
  * Valida la imagen frontal del DPI usando AWS
  * @param processId - ID del proceso de verificación
  * @param frontImage - Imagen frontal del DPI capturada
  * @returns Promesa con el resultado de la validación
  */
  async validateFrontDpiWithAws(processId: string, frontImage: File): Promise<any> {
    try {
      console.log('Validating front DPI with process ID:', processId);
      console.log('Image file:', frontImage.name, frontImage.type, frontImage.size);

      // Create FormData object
      const formData = new FormData();
      formData.append('codigo', processId); // Make sure the field name matches exactly what the API expects
      formData.append('file', frontImage); // Add filename parameter

      // Logging for debug
      for (const pair of formData.entries()) {
        console.log(`FormData contains: ${pair[0]}, ${pair[1]}`);
      }

      const response = await axios.post(
        `${this.config.baseUrl}/dpi/api/validateFrontDPIAWS`,
        formData,
        {
          headers: {
            'connection-mg': this.config.connectionId,
            'api-key': this.config.apiKey,
            // Don't set Content-Type, let axios set it with correct boundary for multipart/form-data
          }
        }
      );

      console.log('Validate Front DPI Response:', response.data);

      return {
        success: true,
        ...response.data
      };
    } catch (error: any) {
      console.error('Error al validar imagen frontal del DPI:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Error desconocido'
      };
    }
  }

  /**
  * Valida la imagen trasera del DPI usando AWS
  * @param processId - ID del proceso de verificación
  * @param backImage - Imagen trasera del DPI capturada
  * @returns Promesa con el resultado de la validación
  */
  async validateBackDpiWithAws(processId: string, backImage: File): Promise<any> {
    try {
      console.log('Validating back DPI with process ID:', processId);
      console.log('Image file:', backImage.name, backImage.type, backImage.size);

      // Create FormData object - using the exact same format as front DPI validation
      const formData = new FormData();
      formData.append('codigo', processId); // Use 'codigo' like in front validation
      formData.append('file', backImage);

      // Logging for debug
      for (const pair of formData.entries()) {
        console.log(`FormData contains: ${pair[0]}, ${pair[1]}`);
      }

      const response = await axios.post(
        `${this.config.baseUrl}/dpi/api/validateBackDPIAWS`, // Change endpoint to back DPI validation
        formData,
        {
          headers: {
            'connection-mg': this.config.connectionId,
            'api-key': this.config.apiKey,
            // Don't set Content-Type, let axios set it with correct boundary for multipart/form-data
          }
        }
      );

      console.log('Validate Back DPI Response:', response.data);

      return {
        success: true,
        ...response.data
      };
    } catch (error: any) {
      console.error('Error al validar imagen trasera del DPI:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Error desconocido'
      };
    }
  }


/**
 * Valida el video selfie para verificación de identidad
 * @param processId - ID del proceso de verificación
 * @param selfieVideo - Archivo de video selfie
 * @returns Promesa con el resultado de la validación
 */
async validateSelfieVideo(processId: string, selfieVideo: File): Promise<any> {
  try {
    // Debug logging
    console.log('=== SELFIE VALIDATION START ===');
    console.log('Process ID:', processId);
    console.log('File:', {
      name: selfieVideo.name,
      type: selfieVideo.type,
      size: selfieVideo.size,
      lastModified: new Date(selfieVideo.lastModified).toISOString()
    });
    
    // Check if file exists and is a valid File object
    if (!selfieVideo || !(selfieVideo instanceof File)) {
      console.error('Missing or invalid file object');
      return {
        success: false,
        error: "Archivo no válido o faltante"
      };
    }
    
    // Ensure the file has the correct MIME type
    const fileType = selfieVideo.type;
    const isMP4 = fileType.startsWith('video/mp4');
    const isWebM = fileType.startsWith('video/webm');
    
    if (!isMP4 && !isWebM) {
      console.error('Invalid file type:', fileType);
      return {
        success: false,
        error: "Tipo de archivo no permitido. Solo se aceptan MP4 y WEBM."
      };
    }
    
    // Create a corrected file with proper extension matching the MIME type
    const fileExtension = isMP4 ? 'mp4' : 'webm';
    const correctedFileName = `selfie_video.${fileExtension}`;
    const correctedFile = new File([selfieVideo], correctedFileName, { 
      type: selfieVideo.type,
      lastModified: selfieVideo.lastModified
    });
    
    // Create a preview URL for the video
    const previewUrl = URL.createObjectURL(correctedFile);
    
    // Save reference to the video
    this.lastSelfieVideo = {
      url: previewUrl,
      file: correctedFile,
      filename: correctedFileName
    };
    
    console.log('Corrected file:', {
      name: correctedFile.name,
      type: correctedFile.type,
      size: correctedFile.size
    });
    
    console.log('Preview URL:', previewUrl);
    
    // Create FormData with the standardized file
    const formData = new FormData();
    formData.append('codigo', processId);
    formData.append('file', correctedFile);
    
    // Log FormData contents for debugging
    console.log('FormData entries:');
    for (const pair of formData.entries()) {
      if (pair[1] instanceof File) {
        console.log(`${pair[0]}: [File: ${(pair[1] as File).name}, ${(pair[1] as File).type}, ${(pair[1] as File).size} bytes]`);
      } else {
        console.log(`${pair[0]}: ${pair[1]}`);
      }
    }
    
    // Use the native fetch API to avoid axios transformations
    const response = await fetch(`${this.config.baseUrl}/videoSelfie/api/validate`, {
      method: 'POST',
      headers: {
        // Don't set Content-Type header - browser will set it automatically with boundary for FormData
        'connection-mg': this.config.connectionId,
        'api-key': this.config.apiKey
      },
      body: formData // Use FormData directly
    });
    
    console.log('Response status:', response.status);
    
    // Parse the response as JSON
    let data;
    try {
      data = await response.json();
      console.log('API Response:', data);
    } catch (error) {
      console.error('Error parsing response:', error);
      data = {
        error: true,
        message: 'Error parsing server response'
      };
    }
    
    // Check for error response
    if (!response.ok) {
      console.error('API Error:', data);
      return {
        success: false,
        error: data.message || `Error HTTP ${response.status}`,
        previewUrl: previewUrl,  // Include the preview URL in error response
        filename: correctedFileName,
        ...data
      };
    }
    
    console.log('=== SELFIE VALIDATION SUCCESS ===');
    return {
      success: true,
      previewUrl: previewUrl,  // Include the preview URL in success response
      filename: correctedFileName,
      ...data
    };
  } catch (error: any) {
    console.error('=== SELFIE VALIDATION ERROR ===', error);
    
    // More detailed error logging
    if (error.name) console.error('Error name:', error.name);
    if (error.message) console.error('Error message:', error.message);
    if (error.stack) console.error('Error stack:', error.stack);
    
    return {
      success: false,
      error: error.message || 'Error desconocido'
    };
  }
}
  /**
   * Verifica el estado de un proceso de verificación
   * @param processId - ID del proceso de verificación
   * @returns Promesa con el estado del proceso
   */
  async checkProcessStatus(processId: string): Promise<any> {
    try {
      const response = await axios({
        method: 'GET',
        url: `${this.config.baseUrl}/process/status/${processId}`,
        headers: {
          'connection-mg': this.config.connectionId,
          'api-key': this.config.apiKey
        }
      });

      return {
        success: true,
        ...response.data
      };
    } catch (error: any) {
      console.error('Error al verificar estado del proceso:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Error desconocido'
      };
    }
  }

  /**
   * Sube una imagen como parte del proceso de verificación
   * @param processId - ID del proceso de verificación
   * @param imageType - Tipo de imagen (frontId, backId, selfie)
   * @param imageFile - Archivo de imagen
   * @returns Promesa con el resultado de la carga
   */
  async uploadProcessImage(processId: string, imageType: 'frontId' | 'backId' | 'selfie', imageFile: File): Promise<any> {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('type', imageType);
      formData.append('processId', processId);

      const response = await axios({
        method: 'POST',
        url: `${this.config.baseUrl}/process/upload-image`,
        headers: {
          'connection-mg': this.config.connectionId,
          'api-key': this.config.apiKey
          // Content-Type will be set automatically when using FormData
        },
        data: formData
      });

      return {
        success: true,
        ...response.data
      };
    } catch (error: any) {
      console.error(`Error al subir imagen ${imageType}:`, error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Error desconocido'
      };
    }
  }

  /**
   * Completa el proceso de verificación
   * @param processId - ID del proceso de verificación
   * @returns Promesa con el resultado del proceso
   */
  async completeVerificationProcess(processId: string): Promise<any> {
    try {
      const response = await axios({
        method: 'POST',
        url: `${this.config.baseUrl}/process/complete/${processId}`,
        headers: {
          'Content-Type': 'application/json',
          'connection-mg': this.config.connectionId,
          'api-key': this.config.apiKey
        }
      });

      return {
        success: true,
        ...response.data
      };
    } catch (error: any) {
      console.error('Error al completar proceso de verificación:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Error desconocido'
      };
    }
  }
}



// Exportar una instancia configurada con los valores hardcodeados
export const authService = new AuthenticationService({
  baseUrl: 'http://localhost:3011',
  apiKey: 'metaG-LhV9FNbtlW6P2C6XQypOmh2Vgo6avJSr8UyLoHCRNB8lJb9mTa30AhaVIw4nPmFQOLtOp8wYA3GjjfSICCsqVfmrjN8uNzrbQcIr0bTYtTpFqQ7BasqDRGSasWuGphL1mKDFEz3FGJ6yetYu',
  connectionId: '67abb4f11b7efa02f9a60aac'
});

// Exportar la clase para poder crear instancias personalizadas si se necesita
export default AuthenticationService;