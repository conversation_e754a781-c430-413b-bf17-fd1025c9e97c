import React, { useRef, useState, useEffect } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/ui/modal';
import { cn } from '@/lib/utils';

interface SignatureModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (signatureDataUrl: string) => void;
  title?: string;
  description?: string;
}

export const SignatureModal: React.FC<SignatureModalProps> = ({
  isOpen,
  onClose,
  onSave,
  title = 'Firma del solicitante',
  description = 'Por favor firma en el área indicada',
}) => {
  const sigCanvas = useRef<SignatureCanvas>(null);
  const [isEmpty, setIsEmpty] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Detectar si es un dispositivo móvil
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Ajustar el tamaño del canvas al tamaño del contenedor
  useEffect(() => {
    if (isOpen && containerRef.current) {
      // Añadir clase al body para ocultar el mapa
      document.body.classList.add('modal-open');

      const updateCanvasSize = () => {
        if (containerRef.current) {
          const { clientWidth, clientHeight } = containerRef.current;
          setCanvasSize({
            width: clientWidth,
            height: clientHeight - 100 // Restar espacio para botones y texto
          });
        }
      };

      updateCanvasSize();
      window.addEventListener('resize', updateCanvasSize);

      return () => {
        // Eliminar clase del body al cerrar el modal
        document.body.classList.remove('modal-open');
        window.removeEventListener('resize', updateCanvasSize);
      };
    }

    return () => {
      // Asegurarse de que la clase se elimine si el componente se desmonta
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]);

  // Limpiar la firma
  const clear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
    }
  };

  // Guardar la firma como imagen
  const save = () => {
    if (sigCanvas.current && !isEmpty) {
      const dataURL = sigCanvas.current.getTrimmedCanvas().toDataURL('image/png');
      onSave(dataURL);
      onClose();
    }
  };

  // Verificar si la firma está vacía
  const handleEnd = () => {
    if (sigCanvas.current) {
      setIsEmpty(sigCanvas.current.isEmpty());
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      fullScreen={true}
      className="signature-modal-container"
    >
      <div className="flex flex-col h-full" ref={containerRef}>
        <div className="p-4 text-center">
          <p className="text-gray-600">{description}</p>
          <p className="text-sm text-gray-500 mt-2">
            {isMobile
              ? 'Usa tu dedo para firmar en toda la pantalla'
              : 'Usa el mouse para firmar en toda la pantalla'}
          </p>
        </div>

        <div className="flex-grow border-2 border-gray-300 mx-4 rounded-md overflow-hidden relative">
          {canvasSize.width > 0 && canvasSize.height > 0 && (
            <SignatureCanvas
              ref={sigCanvas}
              penColor="black"
              canvasProps={{
                width: canvasSize.width,
                height: canvasSize.height,
                className: 'signature-canvas',
                style: {
                  width: '100%',
                  height: '100%',
                  backgroundColor: '#f9fafb',
                  touchAction: 'none' // Importante para evitar el desplazamiento en dispositivos táctiles
                }
              }}
              onEnd={handleEnd}
            />
          )}
        </div>

        <div className="p-4 flex space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={clear}
            className="flex-1"
          >
            Borrar
          </Button>
          <Button
            type="button"
            onClick={save}
            disabled={isEmpty}
            className="flex-1"
          >
            Guardar firma
          </Button>
          <Button
            type="button"
            variant="ghost"
            onClick={onClose}
            className="flex-1"
          >
            Cancelar
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SignatureModal;
