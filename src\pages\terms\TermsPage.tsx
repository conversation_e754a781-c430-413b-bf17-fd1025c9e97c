import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useNavigate } from "react-router-dom";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function TermsPage() {
  const navigate = useNavigate();
  const [accepted, setAccepted] = useState(false);
  const [showError, setShowError] = useState(false);
  
  const handleSubmit = () => {
    if (accepted) {
      navigate('/application');
    } else {
      setShowError(true);
    }
  };
  
  return (
    <div className="flex flex-col min-h-screen p-6">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Términos y Condiciones</h1>
      
      <div className="bg-gray-50 p-4 rounded-md mb-6 h-72 overflow-y-auto border border-gray-200">
        <h2 className="font-semibold mb-2">Términos y Condiciones de Uso</h2>
        <p className="text-sm text-gray-600">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.</p>
        <p className="text-sm text-gray-600 mt-2">Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        <p className="text-sm text-gray-600 mt-2">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        <p className="text-sm text-gray-600 mt-2">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.</p>
        
        <h3 className="font-semibold mt-4 mb-2">Política de Privacidad</h3>
        <p className="text-sm text-gray-600">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget aliquam nisl nisl eget nisl.</p>
        <p className="text-sm text-gray-600 mt-2">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
      </div>
      
      {showError && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>
            Debes aceptar los términos y condiciones para continuar.
          </AlertDescription>
        </Alert>
      )}
      
      <div className="flex items-start space-x-3 mb-8">
        <Checkbox 
          id="terms" 
          checked={accepted} 
          onCheckedChange={(checked) => {
            setAccepted(!!checked);
            if (!!checked) setShowError(false);
          }}
          className="mt-1"
        />
        <label
          htmlFor="terms"
          className="text-sm text-gray-700 leading-tight cursor-pointer"
        >
          Acepto los términos y condiciones y la política de privacidad de CredDigital, y autorizo el tratamiento de mis datos personales.
        </label>
      </div>
      
      <div className="mt-auto">
        <Button 
          onClick={handleSubmit}
          className="w-full"
          size="lg"
        >
          Siguiente
        </Button>
      </div>
    </div>
  );
}