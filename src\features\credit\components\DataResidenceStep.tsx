import { useEffect, useState, useRef } from "react";
import { useResidenceFormStore, ResidenceFormState  } from "../types/useResidenceFormStore";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";

interface DataResidenceStepProps {
    onSubmit: () => Promise<void>;
    onNext: () => void;
    onPrevious?: () => void;
}

export function DataResidenceStep({ onSubmit, onNext, onPrevious }: DataResidenceStepProps) {
    const {
        residenceType, departmentId, municipalityId, address,
        livingTime, isPep, isCpe, dependents, vehicleType, setField
    } = useResidenceFormStore();

    const [departments, setDepartments] = useState<{ id: number; name: string }[]>([]);
    const [municipalities, setMunicipalities] = useState<{ id: number; name: string }[]>([]);
    const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);
    const [hasChanges, setHasChanges] = useState(false);

    // Objeto para rastrear con qué campos ha interactuado el usuario
    const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});

    const isFormComplete = () => {
        return (
            residenceType !== null &&
            departmentId !== null &&
            municipalityId !== null &&
            address.trim().length > 0 &&
            livingTime !== null &&
            isPep !== null &&
            isCpe !== null &&
            dependents !== null &&
            vehicleType !== null
        );
    };

    const [hasInteracted, setHasInteracted] = useState(false);

    const handleFieldChange = <K extends keyof Omit<ResidenceFormState, 'setField' | 'reset'>>(
        field: K,
        value: ResidenceFormState[K]
    ) => {
        setField(field, value);
        // Marcar el campo como tocado
        setTouchedFields(prev => ({ ...prev, [field]: true }));
        if (!hasInteracted) setHasInteracted(true);
    };

    const [shownWarnings, setShownWarnings] = useState<Record<string, boolean>>({});

    useEffect(() => {
        if (!hasInteracted) return;

        const requiredFields = [
            { key: "residenceType", value: residenceType },
            { key: "departmentId", value: departmentId },
            { key: "municipalityId", value: municipalityId },
            { key: "address", value: address.trim() },
            { key: "livingTime", value: livingTime },
            { key: "isPep", value: isPep },
            { key: "isCpe", value: isCpe },
            { key: "dependents", value: dependents },
            { key: "vehicleType", value: vehicleType }
        ];

        const newErrors: Record<string, string> = {};
        const newShownWarnings: Record<string, boolean> = { ...shownWarnings };

        requiredFields.forEach(({ key, value }) => {
            const isEmpty = value === null || value === "" || value === undefined;
            // Solo mostrar errores para campos que han sido tocados
            const fieldTouched = touchedFields[key];

            if (isEmpty) {
                // Solo agregar el error si el campo ha sido tocado
                if (fieldTouched) {
                    newErrors[key] = "Este campo es obligatorio.";
                }

                // Solo mostrar toast si el campo ha sido tocado y no se ha mostrado antes
                if (fieldTouched && !newShownWarnings[key]) {
                    toast.warning("Campo requerido", {
                        description: `Debes completar el campo "${key}".`,
                    });
                    newShownWarnings[key] = true;
                }
            } else {
                newShownWarnings[key] = false;
            }
        });

        setValidationErrors(newErrors);
        setShownWarnings(newShownWarnings);
    }, [
        residenceType, departmentId, municipalityId, address,
        livingTime, isPep, isCpe, dependents, vehicleType,
        hasInteracted, touchedFields
    ]);

    const [isFormValid, setIsFormValid] = useState(false);

    useEffect(() => {
        setIsFormValid(isFormComplete());
    }, [
        residenceType, departmentId, municipalityId, address,
        livingTime, isPep, isCpe, dependents, vehicleType
    ]);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handlePrevious = () => {
        if (onPrevious) {
            scrollToTop();
            onPrevious();
        }
    };

    const originalValues = useRef({
        residenceType,
        departmentId,
        municipalityId,
        address,
        livingTime,
        isPep,
        isCpe,
        dependents,
        vehicleType
    });

    useEffect(() => {
        // Simular carga de departamentos desde API
        setDepartments([
            { id: 1, name: "Departamento A" },
            { id: 2, name: "Departamento B" }
        ]);
    }, []);

    useEffect(() => {
        if (departmentId) {
            setMunicipalities([
                { id: 1, name: "Municipio X" },
                { id: 2, name: "Municipio Y" }
            ]);
        } else {
            setMunicipalities([]);
        }
    }, [departmentId]);

    useEffect(() => {
        const current = {
            residenceType,
            departmentId,
            municipalityId,
            address,
            livingTime,
            isPep,
            isCpe,
            dependents,
            vehicleType
        };

        const changed = Object.keys(current).some((key) => {
            // @ts-ignore
            return current[key] !== originalValues.current[key];
        });

        setHasChanges(changed);
    }, [
        residenceType,
        departmentId,
        municipalityId,
        address,
        livingTime,
        isPep,
        isCpe,
        dependents,
        vehicleType
    ]);

    const handleNext = async () => {
        const missingFields: string[] = [];

        if (!residenceType) missingFields.push("residenceType");
        if (!departmentId) missingFields.push("departmentId");
        if (!municipalityId) missingFields.push("municipalityId");
        if (!address.trim()) missingFields.push("address");
        if (!livingTime) missingFields.push("livingTime");
        if (!isPep) missingFields.push("isPep");
        if (!isCpe) missingFields.push("isCpe");
        if (!dependents) missingFields.push("dependents");
        if (!vehicleType) missingFields.push("vehicleType");

        if (missingFields.length > 0) {
            // Al intentar enviar, marcamos todos los campos como tocados
            const allTouched: Record<string, boolean> = {};
            missingFields.forEach((field) => {
                allTouched[field] = true;
            });
            setTouchedFields(prev => ({ ...prev, ...allTouched }));

            const errors: Record<string, string> = {};
            missingFields.forEach((field) => {
                errors[field] = "Este campo es obligatorio.";
            });
            setValidationErrors(errors);

            toast.error("Información incompleta", {
                description: `Por favor completa ${missingFields.length} campo${missingFields.length > 1 ? "s" : ""} requerido${missingFields.length > 1 ? "s" : ""}.`
            });
            return;
        }

        if (!hasChanges) {
            onNext();
            return;
        }

        setIsSubmitting(true);
        setSubmitError(null);

        try {
            await onSubmit();

            originalValues.current = {
                residenceType,
                departmentId,
                municipalityId,
                address,
                livingTime,
                isPep,
                isCpe,
                dependents,
                vehicleType
            };

            setHasChanges(false);
            onNext();
        } catch (err: any) {
            const msg = err?.message || "Error al enviar";
            setSubmitError(msg);
            toast.error("Error", { description: msg });
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="space-y-6">
            <h2 className="text-xl font-semibold text-blue-700 pb-2 border-b">Datos de Residencia</h2>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">1</span>
                    Tipo de residencia
                </h3>
                <div className="mt-4 px-4 space-y-2">

                    <RadioGroup
                        value={residenceType !== null ? residenceType.toString() : ""}
                        onValueChange={(val) => {
                            handleFieldChange("residenceType", Number(val));
                            // Al cambiar el valor, eliminamos el error
                            setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.residenceType;
                                return newErrors;
                            });
                        }}
                        className="flex flex-wrap gap-2"
                    >
                        {/* //{["Alquiler", "Propia", "Propia hipotecada", "Familiar"].map((label, idx) => ( */}

                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="1"
                                id="alquiler"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="alquiler"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Alquiler
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="2"
                                id="propia"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="propia"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                  text-center flex items-center justify-center">
                                Propia
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="3"
                                id="propiahipotecada"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="propiahipotecada"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                  text-center flex items-center justify-center">
                                Propia hipotecada
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="4"
                                id="familiar"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="familiar"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                  peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                  peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                  peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                  text-center flex items-center justify-center">
                                Familiar
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.residenceType && (
                        <p className="text-red-500 text-sm">{validationErrors.residenceType}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">2</span>
                    Departamento
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <select
                        value={departmentId !== null ? departmentId.toString() : ""}
                        onChange={(e) => {
                            handleFieldChange("departmentId", Number(e.target.value));
                            // Al cambiar el valor, eliminamos el error si se seleccionó un valor válido
                            if (e.target.value) {
                                setValidationErrors(prev => {
                                    const newErrors = { ...prev };
                                    delete newErrors.departmentId;
                                    return newErrors;
                                });
                            }
                        }}
                        className={`w-full p-3 border ${validationErrors.departmentId ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    >
                        <option value="">Selecciona un departamento</option>
                        {departments.map((d) => (
                            <option key={d.id} value={d.id}>{d.name}</option>
                        ))}
                    </select>
                    {validationErrors.departmentId && (
                        <p className="text-red-500 text-sm">{validationErrors.departmentId}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">3</span>
                    Municipio
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <select
                        value={municipalityId !== null ? municipalityId.toString() : ""}
                        onChange={(e) => {
                            handleFieldChange("municipalityId", Number(e.target.value));
                            // Al cambiar el valor, eliminamos el error si se seleccionó un valor válido
                            if (e.target.value) {
                                setValidationErrors(prev => {
                                    const newErrors = { ...prev };
                                    delete newErrors.municipalityId;
                                    return newErrors;
                                });
                            }
                        }}
                        className={`w-full p-3 border ${validationErrors.municipalityId ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                    >
                        <option value="">Selecciona un municipio</option>
                        {municipalities.map((m) => (
                            <option key={m.id} value={m.id}>{m.name}</option>
                        ))}
                    </select>
                    {validationErrors.municipalityId && (
                        <p className="text-red-500 text-sm">{validationErrors.municipalityId}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">4</span>
                    Dirección de residencia
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    <input
                        type="text"
                        value={address}
                        onChange={(e) => handleFieldChange("address", e.target.value)}
                        onBlur={() => {
                            // Al perder el foco, validamos el campo
                            const isEmpty = !address.trim();
                            if (isEmpty) {
                                setValidationErrors(prev => ({
                                    ...prev,
                                    address: "Este campo es obligatorio."
                                }));
                            } else {
                                setValidationErrors(prev => {
                                    const newErrors = { ...prev };
                                    delete newErrors.address;
                                    return newErrors;
                                });
                            }
                        }}
                        className={`w-full p-3 border ${validationErrors.address ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-base`}
                        placeholder="Dirección actual"
                    />
                    {validationErrors.address && (
                        <p className="text-red-500 text-sm">{validationErrors.address}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">5</span>
                    Tiempo de vivir allí
                </h3>
                <div className="mt-4 px-4 space-y-2">

                    <RadioGroup
                        value={livingTime !== null ? livingTime.toString() : ""}
                        onValueChange={(val) => {
                            handleFieldChange("livingTime", Number(val));
                            // Al cambiar el valor, eliminamos el error
                            setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.livingTime;
                                return newErrors;
                            });
                        }}
                        className="flex flex-wrap gap-2"
                    >
                        {/* {["Menos a 1 año", "1 año", "2 años", "3 años", "4 años", "5 años", "Mayor a 5 años"].map((label, idx) => ( */}

                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="1"
                                id="Menosa1año"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Menosa1año"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Menos a 1 año
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="2"
                                id="1año"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="1año"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                1 año
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="3"
                                id="2años"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="2años"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                2 años
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="4"
                                id="3años"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="3años"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                3 años
                            </Label>
                        </div>

                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="5"
                                id="4años"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="4años"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                4 años
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="6"
                                id="5años"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="5años"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                5 años
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="7"
                                id="mayor5años"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="mayor5años"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Mayor a 5 años
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.livingTime && (
                        <p className="text-red-500 text-sm">{validationErrors.livingTime}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">6</span>
                    ¿Eres PEP (Persona expuesta politicamente)?
                </h3>
                <div className="mt-4 px-4 space-y-2">

                    <RadioGroup
                        value={isPep !== null ? isPep.toString() : ""}
                        onValueChange={(val) => {
                            handleFieldChange("isPep", Number(val));
                            // Al cambiar el valor, eliminamos el error
                            setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.isPep;
                                return newErrors;
                            });
                        }}
                        className="flex flex-wrap gap-2"
                    >
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="1"
                                id="sipep"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="sipep"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Si
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="2"
                                id="no"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="no"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                No
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.isPep && (
                        <p className="text-red-500 text-sm">{validationErrors.isPep}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">7</span>
                    ¿Eres CPE (Contratista Proveedor del Estado)?
                </h3>
                <div className="mt-4 px-4 space-y-2">

                    <RadioGroup
                        value={isCpe !== null ? isCpe.toString() : ""}
                        onValueChange={(val) => {
                            handleFieldChange("isCpe", Number(val));
                            // Al cambiar el valor, eliminamos el error
                            setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.isCpe;
                                return newErrors;
                            });
                        }}
                        className="flex flex-wrap gap-2"
                    >
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="1"
                                id="sicpe"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="sicpe"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Si
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="2"
                                id="nocpe"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="nocpe"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                No
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.isCpe && (
                        <p className="text-red-500 text-sm">{validationErrors.isCpe}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">8</span>
                    Personas que dependen de sus ingresos
                </h3>
                <div className="mt-4 px-4 space-y-2">

                    <RadioGroup
                        value={dependents !== null ? dependents.toString() : ""}
                        onValueChange={(val) => {
                            handleFieldChange("dependents", Number(val));
                            // Al cambiar el valor, eliminamos el error
                            setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.dependents;
                                return newErrors;
                            });
                        }}
                        className="flex flex-wrap gap-2"
                    >
                        {/* {["Menos a 1 año", "1 año", "2 años", "3 años", "4 años", "5 años", "Mayor a 5 años"].map((label, idx) => ( */}

                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="1"
                                id="Menosa1año"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Menosa1año"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                0 personas
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="2"
                                id="1persona"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="1persona"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                1 persona
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="3"
                                id="2personas"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="2personas"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                2 personas
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="4"
                                id="3personas"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="3personas"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                3 personas
                            </Label>
                        </div>

                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="5"
                                id="4personas"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="4personas"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                4 personas
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="6"
                                id="5personas"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="5personas"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                5 personas
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="7"
                                id="mayor5personas"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="mayor5personas"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Mayor a 5 personas
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.dependents && (
                        <p className="text-red-500 text-sm">{validationErrors.dependents}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <div className="bg-white p-5 rounded-lg border border-gray-100 shadow-sm space-y-4">
                <h3 className="text-lg font-medium text-gray-800 flex items-center">
                    <span className="bg-blue-600 text-white rounded-full w-6 h-6 inline-flex items-center justify-center mr-2 text-sm">9</span>
                    ¿Posees algún tipo de vehículo?
                </h3>
                <div className="mt-4 px-4 space-y-2">
                    {/* {["No", "Automóvil", "Motocicleta", "Camión", "Pick up"].map((label, idx) => ( */}

                    <RadioGroup
                        value={vehicleType !== null ? vehicleType.toString() : ""}
                        onValueChange={(val) => {
                            handleFieldChange("vehicleType", Number(val));
                            // Al cambiar el valor, eliminamos el error
                            setValidationErrors(prev => {
                                const newErrors = { ...prev };
                                delete newErrors.vehicleType;
                                return newErrors;
                            });
                        }}
                        className="flex flex-wrap gap-2"
                    >
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="1"
                                id="No"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="No"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                No
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="2"
                                id="Automovil"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Automovil"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Automovil
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="3"
                                id="Motocicleta"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Motocicleta"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Motocicleta
                            </Label>
                        </div>
                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="4"
                                id="Camion"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Camion"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Camión
                            </Label>
                        </div>

                        <div className="flex items-center space-x-2 flex-1 min-w-[120px]">
                            <RadioGroupItem
                                value="5"
                                id="Pickup"
                                className="peer sr-only"
                            />
                            <Label
                                htmlFor="Pickup"
                                className="flex-1 px-4 py-2 text-sm font-medium rounded-md border border-gray-300 cursor-pointer
                                peer-data-[state=checked]:bg-indigo-600 peer-data-[state=checked]:text-white
                                peer-data-[state=checked]:border-indigo-600 hover:bg-gray-50
                                peer-data-[state=checked]:hover:bg-indigo-700 transition-all
                                text-center flex items-center justify-center">
                                Pickup
                            </Label>
                        </div>
                    </RadioGroup>

                    {validationErrors.vehicleType && (
                        <p className="text-red-500 text-sm">{validationErrors.vehicleType}</p>
                    )}
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4 mr-1 text-blue-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            {/* {submitError && <p className="text-red-500">{submitError}</p>} */}

            {/* Botones de Navegación */}
            <div className="flex justify-between pt-6 mt-8 sticky bottom-0 bg-gradient-to-t from-white py-4">
                {onPrevious ? (
                    <Button
                        onClick={handlePrevious}
                        variant="outline"
                        size="lg"
                        className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
                        disabled={isSubmitting}
                    >
                        Anterior
                    </Button>
                ) : (
                    <div className="w-[140px]"></div> // Espacio reservado para alineación
                )}

                <Button
                    onClick={handleNext}
                    className={`w-full max-w-[140px] ${isSubmitting ? 'bg-blue-400' : isFormValid ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-300'
                        }`}
                    size="lg"
                    disabled={!isFormValid || isSubmitting}
                >
                    {isSubmitting ? (
                        <div className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Enviando
                        </div>
                    ) : (
                        "Siguiente"
                    )}
                </Button>
            </div>


            {/* <div className="flex justify-between pt-6 mt-4">
                {onPrevious ? (
                    <Button onClick={onPrevious} variant="outline" className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50">
                        Anterior
                    </Button>
                ) : <div className="w-[140px]"></div>}

                <Button
                    onClick={handleNext}
                    disabled={isSubmitting}
                    className={`w-full max-w-[140px] ${isSubmitting ? "bg-blue-400" : "bg-blue-600 hover:bg-blue-700"}`}
                >
                    {isSubmitting ? "Enviando..." : "Siguiente"}
                </Button>
            </div> */}
        </div>
    );
}

