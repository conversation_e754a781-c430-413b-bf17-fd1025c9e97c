/* Estilos para el contenedor de confeti */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
  overflow: hidden;
}

/* Estilos para cada pieza de confeti */
.confetti {
  position: absolute;
  width: 8px;
  height: 8px;
  top: -10px;
  border-radius: 0;
  animation: confetti-fall 10s ease-in-out forwards;
  transform: rotate(0deg);
}

/* Diferentes formas para el confeti */
.confetti:nth-child(3n) {
  border-radius: 50%; /* C<PERSON>rculos */
}

.confetti:nth-child(3n+1) {
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%); /* Triángulos */
}

/* Animación para la caída del confeti */
@keyframes confetti-fall {
  0% {
    top: -10px;
    transform: rotate(0deg) translateX(0);
    opacity: 1;
  }
  25% {
    transform: rotate(90deg) translateX(20px);
  }
  50% {
    transform: rotate(180deg) translateX(-20px);
    opacity: 1;
  }
  75% {
    transform: rotate(270deg) translateX(20px);
  }
  100% {
    top: 100vh;
    transform: rotate(360deg) translateX(-20px);
    opacity: 0;
  }
}

/* Ajustes para dispositivos móviles */
@media (max-width: 768px) {
  .confetti {
    width: 6px;
    height: 6px;
  }

  @keyframes confetti-fall {
    0% {
      top: -10px;
      transform: rotate(0deg) translateX(0);
      opacity: 1;
    }
    25% {
      transform: rotate(90deg) translateX(10px);
    }
    50% {
      transform: rotate(180deg) translateX(-10px);
      opacity: 1;
    }
    75% {
      transform: rotate(270deg) translateX(10px);
    }
    100% {
      top: 100vh;
      transform: rotate(360deg) translateX(-10px);
      opacity: 0;
    }
  }
}
