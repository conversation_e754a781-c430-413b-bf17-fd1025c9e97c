import { create } from "zustand";

interface ResidenceFormState {
  residenceType: number | null;
  departmentId: number | null;
  municipalityId: number | null;
  address: string;
  livingTime: number | null;
  isPep: number | null;
  isCpe: number | null;
  dependents: number | null; // <- si aplica
  vehicleType: number | null;

  setField: <K extends keyof Omit<ResidenceFormState, 'setField' | 'reset'>>(
    field: K,
    value: ResidenceFormState[K]
  ) => void;

  reset: () => void;
}

const initialState: Omit<ResidenceFormState, 'setField' | 'reset'> = {
  residenceType: null,
  departmentId: null,
  municipalityId: null,
  address: '',
  livingTime: null,
  isPep: null,
  isCpe: null,
  dependents: null,   // si 0 es válido, mantenlo como 0; si no, pon null
  vehicleType: null,
};

export const useResidenceFormStore = create<ResidenceFormState>((set) => ({
  ...initialState,

  setField: (field, value) =>
    set((state) => ({
      ...state,
      [field]: value,
    })),

  reset: () => set(() => ({ ...initialState })),
}));


export type { ResidenceFormState };
