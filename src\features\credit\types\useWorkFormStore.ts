import { create } from "zustand";

export interface WorkFormState {
  companyName: string;
  companyEntryDate: string;
  companyPhone: string;
  companyDepartmentId: number | null;
  companyMunicipalityId: number | null;
  monthlySalary: number;
  monthlyExpenses: number;
  otherIncome: number;
  remittanceIncome: number;
  hasBankAccount: number | null; // 1 = sí, 2 = no
  laborLetterFile: File | null;
  electricBillFile: File | null;

  nit: number | null;
  economicActivityId: number | null;
  businessOwnershipTypeId: number | null;
  address: string | null;

  setField: <K extends keyof Omit<WorkFormState, 'setField' | 'reset'>>(
    field: K,
    value: WorkFormState[K]
  ) => void;

  reset: () => void;
}

const initialState: Omit<WorkFormState, 'setField' | 'reset'> = {
  companyName: '',
  companyEntryDate: '',
  companyPhone: '',
  companyDepartmentId: null,
  companyMunicipalityId: null,
  monthlySalary: 0,
  monthlyExpenses: 0,
  otherIncome: 0,
  remittanceIncome: 0,
  hasBankAccount: null,
  laborLetterFile: null,
  electricBillFile: null,
  nit : null,
  economicActivityId : null,
  businessOwnershipTypeId : null,
  address : null,

};

export const useWorkFormStore = create<WorkFormState>((set) => ({
  ...initialState,

  setField: (field, value) =>
    set((state) => ({
      ...state,
      [field]: value,
    })),

  reset: () => set(() => ({ ...initialState })),
}));
