import { useCallback, useRef, useState } from "react";
import { Webcam } from "@webcam/react";
import { Button } from "@/components/ui/button";

interface BackIdComponentProps {
  idBackPhoto: File | null;
  setIdBackPhoto: (file: File | null) => void;
  onNext: () => void;
  onPrevious: () => void;
  processId?: string | null;
  validateBackDpi?: (file: File) => Promise<boolean>;
  error?: string | null;
  setError?: (error: string | null) => void;
}

/**
 * Componente para capturar la **cara trasera** del DPI usando la cámara.
 *
 * Instalación previa:
 *   npm i @webcam/react
 *
 * Requiere servir la página por HTTPS (o localhost) para que getUserMedia funcione.
 */
export function BackIdComponent({
  idBackPhoto,
  setIdBackPhoto,
  onNext,
  onPrevious,
  processId,
  validateBackDpi,
  error,
  setError
}: BackIdComponentProps) {
  const [isCameraActive, setIsCameraActive] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationSuccess, setValidationSuccess] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);

  /** `getSnapshot` que entrega <Webcam>. */
  const snapshotGetter = useRef<
    null | ((opts?: { quality?: number }) => string | undefined)
  >(null);

  /** Convierte un data-URL base-64 a File. */
  const base64ToFile = (dataUrl: string, filename: string): File => {
    const [header, data] = dataUrl.split(",");
    const mimeMatch = header.match(/:(.*?);/);
    const mime = mimeMatch ? mimeMatch[1] : "image/png";
    const binary = atob(data);
    const u8arr = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) u8arr[i] = binary.charCodeAt(i);
    return new File([u8arr], filename, { type: mime });
  };

  /** Cuando el usuario pulsa "Capturar". */
  const capture = useCallback(async () => {
    if (!snapshotGetter.current) return;
    const dataUrl = snapshotGetter.current({ quality: 0.92 });
    if (!dataUrl) return;
    const file = base64ToFile(dataUrl, "dpi_back.jpg");
    
    setIdBackPhoto(file);
    setIsCameraActive(false);
    
    // Reset validation states
    if (setError) setError(null);
    setValidationSuccess(false);
    setValidationMessage(null);
    
    // Si tenemos un processId y una función de validación, validar la imagen
    if (processId && validateBackDpi) {
      setIsValidating(true);
      try {
        const isValid = await validateBackDpi(file);
        setIsValidating(false);
        
        // Handle successful validation
        if (isValid) {
          setValidationSuccess(true);
          setValidationMessage("DPI Válido. Puedes continuar al siguiente paso.");
        }
      } catch (err) {
        console.error("Error en validación:", err);
        setIsValidating(false);
        if (setError) setError("Error al procesar la imagen del reverso del DPI");
      }
    }
  }, [setIdBackPhoto, processId, validateBackDpi, setError]);

  const activateCamera = () => {
    setIdBackPhoto(null);
    if (setError) setError(null);
    setValidationSuccess(false);
    setValidationMessage(null);
    setIsCameraActive(true);
  };

  const retakePhoto = () => {
    setIdBackPhoto(null);
    if (setError) setError(null);
    setValidationSuccess(false);
    setValidationMessage(null);
    setIsCameraActive(true);
  };

  // Instead of directly using onNext, create a handleNext function
  // to ensure proper encapsulation of this component's logic
  const handleNext = () => {
    // Only proceed if we have a photo, validation is successful (or no validation needed),
    // and there's no error
    if (idBackPhoto && !isValidating && (!error) && (!processId || !validateBackDpi || validationSuccess)) {
      // Now call the parent component's onNext function
      onNext();
    } else {
      console.log("Cannot proceed: validation requirements not met");
    }
  };

  // Computed value for checking if the Next button should be disabled
  const isNextButtonDisabled = Boolean(
    !idBackPhoto || 
    isValidating || 
    Boolean(error) || 
    (processId && validateBackDpi && !validationSuccess)
  );

  return (
    <div className="space-y-6">
      {/* ────────────── Encabezado ────────────── */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-5 rounded-xl border border-blue-100">
        <div className="flex items-center space-x-3 mb-2">
          <div className="bg-indigo-100 p-2 rounded-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 text-indigo-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-indigo-800">
            Toma una foto del reverso de tu DPI
          </h3>
        </div>
        <p className="text-gray-700 text-sm ml-11 mb-3">
          Asegúrate de que el código de barras y los textos sean legibles
        </p>
      </div>

      {/* Mensaje de éxito */}
      {validationSuccess && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-green-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clipRule="evenodd"
            />
          </svg>
          {validationMessage || "DPI Válido. Puedes continuar al siguiente paso."}
        </div>
      )}

      {/* Error de validación */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-red-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
          {error}
        </div>
      )}

      {/* ────────────── Contenedor principal ────────────── */}
      <div className="bg-white p-6 rounded-xl border border-gray-200 shadow-sm flex flex-col items-center space-y-6">
        {/* Vista previa / cámara */}
        <div className="w-full max-w-md aspect-[3/2] bg-gray-100 rounded-lg relative overflow-hidden">
          {isCameraActive && (
            <Webcam
              frontCamera={false}
              mirrored={false}
              videoConstraints={{ facingMode: "environment" }}
              className="absolute inset-0 w-full h-full object-cover"
            >
              {({ getSnapshot }) => {
                snapshotGetter.current = getSnapshot;
                return null;
              }}
            </Webcam>
          )}

          {!isCameraActive && idBackPhoto && (
            <img
              src={URL.createObjectURL(idBackPhoto)}
              alt="Captura del reverso del DPI"
              className="absolute inset-0 w-full h-full object-cover rounded-lg"
            />
          )}

          {/* Guía cuando no hay foto ni cámara */}
          {!isCameraActive && !idBackPhoto && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-[85%] h-[85%] border-2 border-dashed border-indigo-400 rounded-lg flex flex-col items-center justify-center">
                <div className="w-[90%] h-[15%] border border-indigo-300 rounded-md bg-indigo-50 bg-opacity-30" />
                <div className="w-[90%] h-[15%] border border-indigo-300 rounded-md bg-indigo-50 bg-opacity-30 mt-auto mb-2" />
              </div>
            </div>
          )}

          {/* Marcadores de esquina */}
          {!idBackPhoto && (
            <>
              <div className="absolute top-4 left-4 w-8 h-8 border-t-2 border-l-2 border-indigo-500" />
              <div className="absolute top-4 right-4 w-8 h-8 border-t-2 border-r-2 border-indigo-500" />
              <div className="absolute bottom-4 left-4 w-8 h-8 border-b-2 border-l-2 border-indigo-500" />
              <div className="absolute bottom-4 right-4 w-8 h-8 border-b-2 border-r-2 border-indigo-500" />
            </>
          )}

          {/* Indicador de validación */}
          {isValidating && (
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="animate-spin w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-gray-800">Validando documento...</p>
              </div>
            </div>
          )}

          {/* Indicador de éxito sobre la foto */}
          {validationSuccess && idBackPhoto && (
            <div className="absolute bottom-3 right-3 bg-green-500 text-white p-2 rounded-full shadow-lg">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          )}
        </div>

        {/* Instrucciones */}
        <ul className="text-sm text-gray-600 space-y-2 w-full max-w-md">
          {[
            "Alinea el documento dentro del marco",
            "Evita reflejos y sombras en el código de barras",
            "Mantén el teléfono paralelo al documento",
          ].map((text) => (
            <li className="flex items-start space-x-2" key={text}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-indigo-500 flex-shrink-0"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4"
                />
              </svg>
              <span>{text}</span>
            </li>
          ))}
        </ul>

        {/* Botón de captura */}
        {idBackPhoto ? (
          <Button
            onClick={retakePhoto}
            variant="outline"
            className="flex items-center gap-2"
            disabled={isValidating}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Tomar otra foto
          </Button>
        ) : (
          <Button
            onClick={isCameraActive ? capture : activateCamera}
            className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700 text-white"
            disabled={isValidating}
          >
            {isCameraActive ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
                Capturar
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                Tomar foto
              </>
            )}
          </Button>
        )}
      </div>

      {/* ────────────── Navegación inferior ────────────── */}
      <div className="flex justify-between pt-4">
        <Button
          onClick={onPrevious}
          variant="outline"
          size="lg"
          className="w-full max-w-[140px] border-gray-300 text-gray-700 hover:bg-gray-50"
          disabled={isValidating}
          type="button"
        >
          Anterior
        </Button>
        <Button
          onClick={handleNext}  // Use the local handler, not onNext directly
          className={`w-full max-w-[140px] ${
            validationSuccess
              ? "bg-green-600 hover:bg-green-700"
              : "bg-indigo-600 hover:bg-indigo-700"
          } text-white`}
          size="lg"
          disabled={isNextButtonDisabled}
          type="button"
        >
          {validationSuccess ? (
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Siguiente
            </div>
          ) : (
            "Siguiente"
          )}
        </Button>
      </div>
    </div>
  );
}