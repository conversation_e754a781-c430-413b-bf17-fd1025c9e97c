import React, { useRef, useState, useEffect } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface SignaturePadProps {
  onSave: (signatureDataUrl: string) => void;
  width?: number | string;
  height?: number;
  className?: string;
  label?: string;
  clearLabel?: string;
  saveLabel?: string;
  required?: boolean;
}

export const SignaturePad: React.FC<SignaturePadProps> = ({
  onSave,
  width = '100%',
  height = 200,
  className = '',
  label = 'Firma aquí',
  clearLabel = 'Borrar',
  saveLabel = 'Guardar',
  required = false,
}) => {
  const sigCanvas = useRef<SignatureCanvas>(null);
  const [isEmpty, setIsEmpty] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Detectar si es un dispositivo móvil
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Limpiar la firma
  const clear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
    }
  };

  // Guardar la firma como imagen
  const save = () => {
    if (sigCanvas.current && !isEmpty) {
      const dataURL = sigCanvas.current.getTrimmedCanvas().toDataURL('image/png');
      onSave(dataURL);
    }
  };

  // Verificar si la firma está vacía
  const handleEnd = () => {
    if (sigCanvas.current) {
      setIsEmpty(sigCanvas.current.isEmpty());
    }
  };

  return (
    <div className={cn('flex flex-col space-y-2', className)}>
      <div className="flex justify-between items-center mb-1">
        <label className="text-sm font-medium text-gray-700">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      </div>
      
      <div className="border-2 border-gray-300 rounded-md overflow-hidden">
        <SignatureCanvas
          ref={sigCanvas}
          penColor="black"
          canvasProps={{
            width: width,
            height: height,
            className: 'signature-canvas',
            style: { 
              width: '100%', 
              height: `${height}px`,
              backgroundColor: '#f9fafb'
            }
          }}
          onEnd={handleEnd}
        />
      </div>
      
      {isEmpty && (
        <p className="text-sm text-gray-500 mt-1">
          {isMobile 
            ? 'Usa tu dedo para firmar aquí' 
            : 'Usa el mouse para firmar aquí'}
        </p>
      )}
      
      <div className="flex space-x-2 mt-2">
        <Button 
          type="button" 
          variant="outline" 
          onClick={clear}
          className="flex-1"
        >
          {clearLabel}
        </Button>
        <Button 
          type="button" 
          onClick={save} 
          disabled={isEmpty}
          className="flex-1"
        >
          {saveLabel}
        </Button>
      </div>
    </div>
  );
};

export default SignaturePad;
