<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Leaflet Test</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
  <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
  <style>
    #map {
      height: 400px;
      width: 100%;
    }
    body {
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    h1 {
      color: #3b82f6;
    }
  </style>
</head>
<body>
  <h1>Leaflet Map Test</h1>
  <div id="map"></div>

  <script>
    // Initialize the map
    const map = L.map('map').setView([14.6349, -90.5069], 10);

    // Add the OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Add a marker for Guatemala City
    L.marker([14.6349, -90.5069]).addTo(map)
      .bindPopup('Guatemala City')
      .openPopup();

    // Add some branch markers
    const branches = [
      { name: "Sucursal Norte", coordinates: [14.6549, -90.4869] },
      { name: "Sucursal Sur", coordinates: [14.6149, -90.5269] },
      { name: "Sucursal Este", coordinates: [14.6349, -90.4769] },
      { name: "Sucursal Oeste", coordinates: [14.6349, -90.5369] }
    ];

    branches.forEach(branch => {
      L.marker(branch.coordinates).addTo(map)
        .bindPopup(branch.name);
    });
  </script>
</body>
</html>
